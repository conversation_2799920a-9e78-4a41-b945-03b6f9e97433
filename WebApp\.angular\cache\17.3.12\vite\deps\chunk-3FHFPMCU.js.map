{"version": 3, "sources": ["../../../../../node_modules/ng-zorro-antd/fesm2022/ng-zorro-antd-menu.mjs"], "sourcesContent": ["import { __decorate } from 'tslib';\nimport * as i0 from '@angular/core';\nimport { InjectionToken, Injectable, SkipSelf, Optional, Inject, Component, ChangeDetectionStrategy, ViewEncapsulation, Input, ContentChildren, EventEmitter, Output, ElementRef, Host, ViewChild, forwardRef, inject, Directive, NgModule } from '@angular/core';\nimport { Subject, BehaviorSubject, merge, combineLatest } from 'rxjs';\nimport { map, mergeMap, filter, mapTo, auditTime, distinctUntilChanged, takeUntil, startWith, switchMap } from 'rxjs/operators';\nimport { InputBoolean } from 'ng-zorro-antd/core/util';\nimport * as i4 from '@angular/router';\nimport { NavigationEnd, RouterLink } from '@angular/router';\nimport * as i1 from '@angular/cdk/bidi';\nimport * as i6 from '@angular/cdk/overlay';\nimport { CdkOverlayOrigin, OverlayModule } from '@angular/cdk/overlay';\nimport * as i5 from 'ng-zorro-antd/core/no-animation';\nimport { NzNoAnimationDirective } from 'ng-zorro-antd/core/no-animation';\nimport { POSITION_MAP, getPlacementName } from 'ng-zorro-antd/core/overlay';\nimport { NgTemplateOutlet, NgClass } from '@angular/common';\nimport { collapseMotion, zoomBigMotion, slideMotion } from 'ng-zorro-antd/core/animation';\nimport * as i3 from 'ng-zorro-antd/core/outlet';\nimport { NzOutletModule } from 'ng-zorro-antd/core/outlet';\nimport * as i2 from 'ng-zorro-antd/icon';\nimport { NzIconModule } from 'ng-zorro-antd/icon';\nimport * as i3$1 from '@angular/cdk/platform';\n\n/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\nconst _c0 = [\"nz-menu-item\", \"\"];\nconst _c1 = [\"*\"];\nconst _c2 = [\"nz-submenu-inline-child\", \"\"];\nfunction NzSubmenuInlineChildComponent_ng_template_0_Template(rf, ctx) {}\nconst _c3 = [\"nz-submenu-none-inline-child\", \"\"];\nfunction NzSubmenuNoneInlineChildComponent_ng_template_1_Template(rf, ctx) {}\nconst _c4 = [\"nz-submenu-title\", \"\"];\nfunction NzSubMenuTitleComponent_Conditional_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"span\", 0);\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"nzType\", ctx_r0.nzIcon);\n  }\n}\nfunction NzSubMenuTitleComponent_ng_container_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"span\", 3);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r0.nzTitle);\n  }\n}\nfunction NzSubMenuTitleComponent_Conditional_3_Case_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"span\", 4);\n  }\n}\nfunction NzSubMenuTitleComponent_Conditional_3_Case_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"span\", 5);\n  }\n}\nfunction NzSubMenuTitleComponent_Conditional_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 2);\n    i0.ɵɵtemplate(1, NzSubMenuTitleComponent_Conditional_3_Case_1_Template, 1, 0)(2, NzSubMenuTitleComponent_Conditional_3_Case_2_Template, 1, 0);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    let tmp_1_0;\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵconditional(1, (tmp_1_0 = ctx_r0.dir) === \"rtl\" ? 1 : 2);\n  }\n}\nfunction NzSubMenuTitleComponent_Conditional_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"span\", 6);\n  }\n}\nconst _c5 = [\"nz-submenu\", \"\"];\nconst _c6 = [[[\"\", \"title\", \"\"]], \"*\"];\nconst _c7 = [\"[title]\", \"*\"];\nfunction NzSubMenuComponent_Conditional_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵprojection(0);\n  }\n}\nfunction NzSubMenuComponent_Conditional_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"div\", 3);\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    const subMenuTemplate_r3 = i0.ɵɵreference(6);\n    i0.ɵɵproperty(\"mode\", ctx_r1.mode)(\"nzOpen\", ctx_r1.nzOpen)(\"@.disabled\", !!(ctx_r1.noAnimation == null ? null : ctx_r1.noAnimation.nzNoAnimation))(\"nzNoAnimation\", ctx_r1.noAnimation == null ? null : ctx_r1.noAnimation.nzNoAnimation)(\"menuClass\", ctx_r1.nzMenuClassName)(\"templateOutlet\", subMenuTemplate_r3);\n  }\n}\nfunction NzSubMenuComponent_Conditional_4_ng_template_0_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r5 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 5);\n    i0.ɵɵlistener(\"subMenuMouseState\", function NzSubMenuComponent_Conditional_4_ng_template_0_Template_div_subMenuMouseState_0_listener($event) {\n      i0.ɵɵrestoreView(_r5);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.setMouseEnterState($event));\n    });\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    const subMenuTemplate_r3 = i0.ɵɵreference(6);\n    i0.ɵɵproperty(\"theme\", ctx_r1.theme)(\"mode\", ctx_r1.mode)(\"nzOpen\", ctx_r1.nzOpen)(\"position\", ctx_r1.position)(\"nzDisabled\", ctx_r1.nzDisabled)(\"isMenuInsideDropDown\", ctx_r1.isMenuInsideDropDown)(\"templateOutlet\", subMenuTemplate_r3)(\"menuClass\", ctx_r1.nzMenuClassName)(\"@.disabled\", !!(ctx_r1.noAnimation == null ? null : ctx_r1.noAnimation.nzNoAnimation))(\"nzNoAnimation\", ctx_r1.noAnimation == null ? null : ctx_r1.noAnimation.nzNoAnimation);\n  }\n}\nfunction NzSubMenuComponent_Conditional_4_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r4 = i0.ɵɵgetCurrentView();\n    i0.ɵɵtemplate(0, NzSubMenuComponent_Conditional_4_ng_template_0_Template, 1, 10, \"ng-template\", 4);\n    i0.ɵɵlistener(\"positionChange\", function NzSubMenuComponent_Conditional_4_Template_ng_template_positionChange_0_listener($event) {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.onPositionChange($event));\n    });\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    const origin_r6 = i0.ɵɵreference(1);\n    i0.ɵɵproperty(\"cdkConnectedOverlayPositions\", ctx_r1.overlayPositions)(\"cdkConnectedOverlayOrigin\", origin_r6)(\"cdkConnectedOverlayWidth\", ctx_r1.triggerWidth)(\"cdkConnectedOverlayOpen\", ctx_r1.nzOpen)(\"cdkConnectedOverlayTransformOriginOn\", \".ant-menu-submenu\");\n  }\n}\nfunction NzSubMenuComponent_ng_template_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵprojection(0, 1);\n  }\n}\nconst _c8 = [\"titleElement\"];\nconst _c9 = [\"nz-menu-group\", \"\"];\nconst _c10 = [\"*\", [[\"\", \"title\", \"\"]]];\nconst _c11 = [\"*\", \"[title]\"];\nfunction NzMenuGroupComponent_ng_container_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(ctx_r0.nzTitle);\n  }\n}\nfunction NzMenuGroupComponent_Conditional_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵprojection(0, 1);\n  }\n}\nconst NzIsMenuInsideDropDownToken = new InjectionToken('NzIsInDropDownMenuToken');\nconst NzMenuServiceLocalToken = new InjectionToken('NzMenuServiceLocalToken');\n\n/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\nclass MenuService {\n  constructor() {\n    /** all descendant menu click **/\n    this.descendantMenuItemClick$ = new Subject();\n    /** child menu item click **/\n    this.childMenuItemClick$ = new Subject();\n    this.theme$ = new BehaviorSubject('light');\n    this.mode$ = new BehaviorSubject('vertical');\n    this.inlineIndent$ = new BehaviorSubject(24);\n    this.isChildSubMenuOpen$ = new BehaviorSubject(false);\n  }\n  onDescendantMenuItemClick(menu) {\n    this.descendantMenuItemClick$.next(menu);\n  }\n  onChildMenuItemClick(menu) {\n    this.childMenuItemClick$.next(menu);\n  }\n  setMode(mode) {\n    this.mode$.next(mode);\n  }\n  setTheme(theme) {\n    this.theme$.next(theme);\n  }\n  setInlineIndent(indent) {\n    this.inlineIndent$.next(indent);\n  }\n  static {\n    this.ɵfac = function MenuService_Factory(t) {\n      return new (t || MenuService)();\n    };\n  }\n  static {\n    this.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n      token: MenuService,\n      factory: MenuService.ɵfac\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MenuService, [{\n    type: Injectable\n  }], null, null);\n})();\n\n/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\nclass NzSubmenuService {\n  /**\n   * menu item inside submenu clicked\n   *\n   * @param menu\n   */\n  onChildMenuItemClick(menu) {\n    this.childMenuItemClick$.next(menu);\n  }\n  setOpenStateWithoutDebounce(value) {\n    this.isCurrentSubMenuOpen$.next(value);\n  }\n  setMouseEnterTitleOrOverlayState(value) {\n    this.isMouseEnterTitleOrOverlay$.next(value);\n  }\n  constructor(nzHostSubmenuService, nzMenuService, isMenuInsideDropDown) {\n    this.nzHostSubmenuService = nzHostSubmenuService;\n    this.nzMenuService = nzMenuService;\n    this.isMenuInsideDropDown = isMenuInsideDropDown;\n    this.mode$ = this.nzMenuService.mode$.pipe(map(mode => {\n      if (mode === 'inline') {\n        return 'inline';\n        /** if inside another submenu, set the mode to vertical **/\n      } else if (mode === 'vertical' || this.nzHostSubmenuService) {\n        return 'vertical';\n      } else {\n        return 'horizontal';\n      }\n    }));\n    this.level = 1;\n    this.isCurrentSubMenuOpen$ = new BehaviorSubject(false);\n    this.isChildSubMenuOpen$ = new BehaviorSubject(false);\n    /** submenu title & overlay mouse enter status **/\n    this.isMouseEnterTitleOrOverlay$ = new Subject();\n    this.childMenuItemClick$ = new Subject();\n    this.destroy$ = new Subject();\n    if (this.nzHostSubmenuService) {\n      this.level = this.nzHostSubmenuService.level + 1;\n    }\n    /** close if menu item clicked **/\n    const isClosedByMenuItemClick = this.childMenuItemClick$.pipe(mergeMap(() => this.mode$), filter(mode => mode !== 'inline' || this.isMenuInsideDropDown), mapTo(false));\n    const isCurrentSubmenuOpen$ = merge(this.isMouseEnterTitleOrOverlay$, isClosedByMenuItemClick);\n    /** combine the child submenu status with current submenu status to calculate host submenu open **/\n    const isSubMenuOpenWithDebounce$ = combineLatest([this.isChildSubMenuOpen$, isCurrentSubmenuOpen$]).pipe(map(([isChildSubMenuOpen, isCurrentSubmenuOpen]) => isChildSubMenuOpen || isCurrentSubmenuOpen), auditTime(150), distinctUntilChanged(), takeUntil(this.destroy$));\n    isSubMenuOpenWithDebounce$.pipe(distinctUntilChanged()).subscribe(data => {\n      this.setOpenStateWithoutDebounce(data);\n      if (this.nzHostSubmenuService) {\n        /** set parent submenu's child submenu open status **/\n        this.nzHostSubmenuService.isChildSubMenuOpen$.next(data);\n      } else {\n        this.nzMenuService.isChildSubMenuOpen$.next(data);\n      }\n    });\n  }\n  ngOnDestroy() {\n    this.destroy$.next();\n    this.destroy$.complete();\n  }\n  static {\n    this.ɵfac = function NzSubmenuService_Factory(t) {\n      return new (t || NzSubmenuService)(i0.ɵɵinject(NzSubmenuService, 12), i0.ɵɵinject(MenuService), i0.ɵɵinject(NzIsMenuInsideDropDownToken));\n    };\n  }\n  static {\n    this.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n      token: NzSubmenuService,\n      factory: NzSubmenuService.ɵfac\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(NzSubmenuService, [{\n    type: Injectable\n  }], () => [{\n    type: NzSubmenuService,\n    decorators: [{\n      type: SkipSelf\n    }, {\n      type: Optional\n    }]\n  }, {\n    type: MenuService\n  }, {\n    type: undefined,\n    decorators: [{\n      type: Inject,\n      args: [NzIsMenuInsideDropDownToken]\n    }]\n  }], null);\n})();\nclass NzMenuItemComponent {\n  /** clear all item selected status except this */\n  clickMenuItem(e) {\n    if (this.nzDisabled) {\n      e.preventDefault();\n      e.stopPropagation();\n    } else {\n      this.nzMenuService.onDescendantMenuItemClick(this);\n      if (this.nzSubmenuService) {\n        /** menu item inside the submenu **/\n        this.nzSubmenuService.onChildMenuItemClick(this);\n      } else {\n        /** menu item inside the root menu **/\n        this.nzMenuService.onChildMenuItemClick(this);\n      }\n    }\n  }\n  setSelectedState(value) {\n    this.nzSelected = value;\n    this.selected$.next(value);\n  }\n  updateRouterActive() {\n    if (!this.listOfRouterLink || !this.router || !this.router.navigated || !this.nzMatchRouter) {\n      return;\n    }\n    Promise.resolve().then(() => {\n      const hasActiveLinks = this.hasActiveLinks();\n      if (this.nzSelected !== hasActiveLinks) {\n        this.nzSelected = hasActiveLinks;\n        this.setSelectedState(this.nzSelected);\n        this.cdr.markForCheck();\n      }\n    });\n  }\n  hasActiveLinks() {\n    const isActiveCheckFn = this.isLinkActive(this.router);\n    return this.routerLink && isActiveCheckFn(this.routerLink) || this.listOfRouterLink.some(isActiveCheckFn);\n  }\n  isLinkActive(router) {\n    return link => router.isActive(link.urlTree || '', {\n      paths: this.nzMatchRouterExact ? 'exact' : 'subset',\n      queryParams: this.nzMatchRouterExact ? 'exact' : 'subset',\n      fragment: 'ignored',\n      matrixParams: 'ignored'\n    });\n  }\n  constructor(nzMenuService, cdr, nzSubmenuService, isMenuInsideDropDown, directionality, routerLink, router) {\n    this.nzMenuService = nzMenuService;\n    this.cdr = cdr;\n    this.nzSubmenuService = nzSubmenuService;\n    this.isMenuInsideDropDown = isMenuInsideDropDown;\n    this.directionality = directionality;\n    this.routerLink = routerLink;\n    this.router = router;\n    this.destroy$ = new Subject();\n    this.level = this.nzSubmenuService ? this.nzSubmenuService.level + 1 : 1;\n    this.selected$ = new Subject();\n    this.inlinePaddingLeft = null;\n    this.dir = 'ltr';\n    this.nzDisabled = false;\n    this.nzSelected = false;\n    this.nzDanger = false;\n    this.nzMatchRouterExact = false;\n    this.nzMatchRouter = false;\n    if (router) {\n      this.router.events.pipe(takeUntil(this.destroy$), filter(e => e instanceof NavigationEnd)).subscribe(() => {\n        this.updateRouterActive();\n      });\n    }\n  }\n  ngOnInit() {\n    /** store origin padding in padding */\n    combineLatest([this.nzMenuService.mode$, this.nzMenuService.inlineIndent$]).pipe(takeUntil(this.destroy$)).subscribe(([mode, inlineIndent]) => {\n      this.inlinePaddingLeft = mode === 'inline' ? this.level * inlineIndent : null;\n    });\n    this.dir = this.directionality.value;\n    this.directionality.change?.pipe(takeUntil(this.destroy$)).subscribe(direction => {\n      this.dir = direction;\n    });\n  }\n  ngAfterContentInit() {\n    this.listOfRouterLink.changes.pipe(takeUntil(this.destroy$)).subscribe(() => this.updateRouterActive());\n    this.updateRouterActive();\n  }\n  ngOnChanges(changes) {\n    if (changes.nzSelected) {\n      this.setSelectedState(this.nzSelected);\n    }\n  }\n  ngOnDestroy() {\n    this.destroy$.next(true);\n    this.destroy$.complete();\n  }\n  static {\n    this.ɵfac = function NzMenuItemComponent_Factory(t) {\n      return new (t || NzMenuItemComponent)(i0.ɵɵdirectiveInject(MenuService), i0.ɵɵdirectiveInject(i0.ChangeDetectorRef), i0.ɵɵdirectiveInject(NzSubmenuService, 8), i0.ɵɵdirectiveInject(NzIsMenuInsideDropDownToken), i0.ɵɵdirectiveInject(i1.Directionality, 8), i0.ɵɵdirectiveInject(i4.RouterLink, 8), i0.ɵɵdirectiveInject(i4.Router, 8));\n    };\n  }\n  static {\n    this.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n      type: NzMenuItemComponent,\n      selectors: [[\"\", \"nz-menu-item\", \"\"]],\n      contentQueries: function NzMenuItemComponent_ContentQueries(rf, ctx, dirIndex) {\n        if (rf & 1) {\n          i0.ɵɵcontentQuery(dirIndex, RouterLink, 5);\n        }\n        if (rf & 2) {\n          let _t;\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.listOfRouterLink = _t);\n        }\n      },\n      hostVars: 20,\n      hostBindings: function NzMenuItemComponent_HostBindings(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵlistener(\"click\", function NzMenuItemComponent_click_HostBindingHandler($event) {\n            return ctx.clickMenuItem($event);\n          });\n        }\n        if (rf & 2) {\n          i0.ɵɵstyleProp(\"padding-left\", ctx.dir === \"rtl\" ? null : ctx.nzPaddingLeft || ctx.inlinePaddingLeft, \"px\")(\"padding-right\", ctx.dir === \"rtl\" ? ctx.nzPaddingLeft || ctx.inlinePaddingLeft : null, \"px\");\n          i0.ɵɵclassProp(\"ant-dropdown-menu-item\", ctx.isMenuInsideDropDown)(\"ant-dropdown-menu-item-selected\", ctx.isMenuInsideDropDown && ctx.nzSelected)(\"ant-dropdown-menu-item-danger\", ctx.isMenuInsideDropDown && ctx.nzDanger)(\"ant-dropdown-menu-item-disabled\", ctx.isMenuInsideDropDown && ctx.nzDisabled)(\"ant-menu-item\", !ctx.isMenuInsideDropDown)(\"ant-menu-item-selected\", !ctx.isMenuInsideDropDown && ctx.nzSelected)(\"ant-menu-item-danger\", !ctx.isMenuInsideDropDown && ctx.nzDanger)(\"ant-menu-item-disabled\", !ctx.isMenuInsideDropDown && ctx.nzDisabled);\n        }\n      },\n      inputs: {\n        nzPaddingLeft: \"nzPaddingLeft\",\n        nzDisabled: \"nzDisabled\",\n        nzSelected: \"nzSelected\",\n        nzDanger: \"nzDanger\",\n        nzMatchRouterExact: \"nzMatchRouterExact\",\n        nzMatchRouter: \"nzMatchRouter\"\n      },\n      exportAs: [\"nzMenuItem\"],\n      standalone: true,\n      features: [i0.ɵɵNgOnChangesFeature, i0.ɵɵStandaloneFeature],\n      attrs: _c0,\n      ngContentSelectors: _c1,\n      decls: 2,\n      vars: 0,\n      consts: [[1, \"ant-menu-title-content\"]],\n      template: function NzMenuItemComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵprojectionDef();\n          i0.ɵɵelementStart(0, \"span\", 0);\n          i0.ɵɵprojection(1);\n          i0.ɵɵelementEnd();\n        }\n      },\n      encapsulation: 2,\n      changeDetection: 0\n    });\n  }\n}\n__decorate([InputBoolean()], NzMenuItemComponent.prototype, \"nzDisabled\", void 0);\n__decorate([InputBoolean()], NzMenuItemComponent.prototype, \"nzSelected\", void 0);\n__decorate([InputBoolean()], NzMenuItemComponent.prototype, \"nzDanger\", void 0);\n__decorate([InputBoolean()], NzMenuItemComponent.prototype, \"nzMatchRouterExact\", void 0);\n__decorate([InputBoolean()], NzMenuItemComponent.prototype, \"nzMatchRouter\", void 0);\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(NzMenuItemComponent, [{\n    type: Component,\n    args: [{\n      selector: '[nz-menu-item]',\n      exportAs: 'nzMenuItem',\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      encapsulation: ViewEncapsulation.None,\n      preserveWhitespaces: false,\n      template: `\n    <span class=\"ant-menu-title-content\">\n      <ng-content></ng-content>\n    </span>\n  `,\n      host: {\n        '[class.ant-dropdown-menu-item]': `isMenuInsideDropDown`,\n        '[class.ant-dropdown-menu-item-selected]': `isMenuInsideDropDown && nzSelected`,\n        '[class.ant-dropdown-menu-item-danger]': `isMenuInsideDropDown && nzDanger`,\n        '[class.ant-dropdown-menu-item-disabled]': `isMenuInsideDropDown && nzDisabled`,\n        '[class.ant-menu-item]': `!isMenuInsideDropDown`,\n        '[class.ant-menu-item-selected]': `!isMenuInsideDropDown && nzSelected`,\n        '[class.ant-menu-item-danger]': `!isMenuInsideDropDown && nzDanger`,\n        '[class.ant-menu-item-disabled]': `!isMenuInsideDropDown && nzDisabled`,\n        '[style.paddingLeft.px]': `dir === 'rtl' ? null : nzPaddingLeft || inlinePaddingLeft`,\n        '[style.paddingRight.px]': `dir === 'rtl' ? nzPaddingLeft || inlinePaddingLeft : null`,\n        '(click)': 'clickMenuItem($event)'\n      },\n      standalone: true\n    }]\n  }], () => [{\n    type: MenuService\n  }, {\n    type: i0.ChangeDetectorRef\n  }, {\n    type: NzSubmenuService,\n    decorators: [{\n      type: Optional\n    }]\n  }, {\n    type: undefined,\n    decorators: [{\n      type: Inject,\n      args: [NzIsMenuInsideDropDownToken]\n    }]\n  }, {\n    type: i1.Directionality,\n    decorators: [{\n      type: Optional\n    }]\n  }, {\n    type: i4.RouterLink,\n    decorators: [{\n      type: Optional\n    }]\n  }, {\n    type: i4.Router,\n    decorators: [{\n      type: Optional\n    }]\n  }], {\n    nzPaddingLeft: [{\n      type: Input\n    }],\n    nzDisabled: [{\n      type: Input\n    }],\n    nzSelected: [{\n      type: Input\n    }],\n    nzDanger: [{\n      type: Input\n    }],\n    nzMatchRouterExact: [{\n      type: Input\n    }],\n    nzMatchRouter: [{\n      type: Input\n    }],\n    listOfRouterLink: [{\n      type: ContentChildren,\n      args: [RouterLink, {\n        descendants: true\n      }]\n    }]\n  });\n})();\nclass NzSubmenuInlineChildComponent {\n  constructor(elementRef, renderer, directionality) {\n    this.elementRef = elementRef;\n    this.renderer = renderer;\n    this.directionality = directionality;\n    this.templateOutlet = null;\n    this.menuClass = '';\n    this.mode = 'vertical';\n    this.nzOpen = false;\n    this.listOfCacheClassName = [];\n    this.expandState = 'collapsed';\n    this.dir = 'ltr';\n    this.destroy$ = new Subject();\n  }\n  calcMotionState() {\n    if (this.nzOpen) {\n      this.expandState = 'expanded';\n    } else {\n      this.expandState = 'collapsed';\n    }\n  }\n  ngOnInit() {\n    this.calcMotionState();\n    this.dir = this.directionality.value;\n    this.directionality.change?.pipe(takeUntil(this.destroy$)).subscribe(direction => {\n      this.dir = direction;\n    });\n  }\n  ngOnChanges(changes) {\n    const {\n      mode,\n      nzOpen,\n      menuClass\n    } = changes;\n    if (mode || nzOpen) {\n      this.calcMotionState();\n    }\n    if (menuClass) {\n      if (this.listOfCacheClassName.length) {\n        this.listOfCacheClassName.filter(item => !!item).forEach(className => {\n          this.renderer.removeClass(this.elementRef.nativeElement, className);\n        });\n      }\n      if (this.menuClass) {\n        this.listOfCacheClassName = this.menuClass.split(' ');\n        this.listOfCacheClassName.filter(item => !!item).forEach(className => {\n          this.renderer.addClass(this.elementRef.nativeElement, className);\n        });\n      }\n    }\n  }\n  ngOnDestroy() {\n    this.destroy$.next();\n    this.destroy$.complete();\n  }\n  static {\n    this.ɵfac = function NzSubmenuInlineChildComponent_Factory(t) {\n      return new (t || NzSubmenuInlineChildComponent)(i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(i0.Renderer2), i0.ɵɵdirectiveInject(i1.Directionality, 8));\n    };\n  }\n  static {\n    this.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n      type: NzSubmenuInlineChildComponent,\n      selectors: [[\"\", \"nz-submenu-inline-child\", \"\"]],\n      hostAttrs: [1, \"ant-menu\", \"ant-menu-inline\", \"ant-menu-sub\"],\n      hostVars: 3,\n      hostBindings: function NzSubmenuInlineChildComponent_HostBindings(rf, ctx) {\n        if (rf & 2) {\n          i0.ɵɵsyntheticHostProperty(\"@collapseMotion\", ctx.expandState);\n          i0.ɵɵclassProp(\"ant-menu-rtl\", ctx.dir === \"rtl\");\n        }\n      },\n      inputs: {\n        templateOutlet: \"templateOutlet\",\n        menuClass: \"menuClass\",\n        mode: \"mode\",\n        nzOpen: \"nzOpen\"\n      },\n      exportAs: [\"nzSubmenuInlineChild\"],\n      standalone: true,\n      features: [i0.ɵɵNgOnChangesFeature, i0.ɵɵStandaloneFeature],\n      attrs: _c2,\n      decls: 1,\n      vars: 1,\n      consts: [[3, \"ngTemplateOutlet\"]],\n      template: function NzSubmenuInlineChildComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵtemplate(0, NzSubmenuInlineChildComponent_ng_template_0_Template, 0, 0, \"ng-template\", 0);\n        }\n        if (rf & 2) {\n          i0.ɵɵproperty(\"ngTemplateOutlet\", ctx.templateOutlet);\n        }\n      },\n      dependencies: [NgTemplateOutlet],\n      encapsulation: 2,\n      data: {\n        animation: [collapseMotion]\n      },\n      changeDetection: 0\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(NzSubmenuInlineChildComponent, [{\n    type: Component,\n    args: [{\n      selector: '[nz-submenu-inline-child]',\n      animations: [collapseMotion],\n      exportAs: 'nzSubmenuInlineChild',\n      encapsulation: ViewEncapsulation.None,\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      template: ` <ng-template [ngTemplateOutlet]=\"templateOutlet\"></ng-template> `,\n      host: {\n        class: 'ant-menu ant-menu-inline ant-menu-sub',\n        '[class.ant-menu-rtl]': `dir === 'rtl'`,\n        '[@collapseMotion]': 'expandState'\n      },\n      imports: [NgTemplateOutlet],\n      standalone: true\n    }]\n  }], () => [{\n    type: i0.ElementRef\n  }, {\n    type: i0.Renderer2\n  }, {\n    type: i1.Directionality,\n    decorators: [{\n      type: Optional\n    }]\n  }], {\n    templateOutlet: [{\n      type: Input\n    }],\n    menuClass: [{\n      type: Input\n    }],\n    mode: [{\n      type: Input\n    }],\n    nzOpen: [{\n      type: Input\n    }]\n  });\n})();\nclass NzSubmenuNoneInlineChildComponent {\n  constructor(directionality) {\n    this.directionality = directionality;\n    this.menuClass = '';\n    this.theme = 'light';\n    this.templateOutlet = null;\n    this.isMenuInsideDropDown = false;\n    this.mode = 'vertical';\n    this.position = 'right';\n    this.nzDisabled = false;\n    this.nzOpen = false;\n    this.subMenuMouseState = new EventEmitter();\n    this.expandState = 'collapsed';\n    this.dir = 'ltr';\n    this.destroy$ = new Subject();\n  }\n  setMouseState(state) {\n    if (!this.nzDisabled) {\n      this.subMenuMouseState.next(state);\n    }\n  }\n  ngOnDestroy() {\n    this.destroy$.next();\n    this.destroy$.complete();\n  }\n  calcMotionState() {\n    if (this.nzOpen) {\n      if (this.mode === 'horizontal') {\n        this.expandState = 'bottom';\n      } else if (this.mode === 'vertical') {\n        this.expandState = 'active';\n      }\n    } else {\n      this.expandState = 'collapsed';\n    }\n  }\n  ngOnInit() {\n    this.calcMotionState();\n    this.dir = this.directionality.value;\n    this.directionality.change?.pipe(takeUntil(this.destroy$)).subscribe(direction => {\n      this.dir = direction;\n    });\n  }\n  ngOnChanges(changes) {\n    const {\n      mode,\n      nzOpen\n    } = changes;\n    if (mode || nzOpen) {\n      this.calcMotionState();\n    }\n  }\n  static {\n    this.ɵfac = function NzSubmenuNoneInlineChildComponent_Factory(t) {\n      return new (t || NzSubmenuNoneInlineChildComponent)(i0.ɵɵdirectiveInject(i1.Directionality, 8));\n    };\n  }\n  static {\n    this.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n      type: NzSubmenuNoneInlineChildComponent,\n      selectors: [[\"\", \"nz-submenu-none-inline-child\", \"\"]],\n      hostAttrs: [1, \"ant-menu-submenu\", \"ant-menu-submenu-popup\"],\n      hostVars: 14,\n      hostBindings: function NzSubmenuNoneInlineChildComponent_HostBindings(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵlistener(\"mouseenter\", function NzSubmenuNoneInlineChildComponent_mouseenter_HostBindingHandler() {\n            return ctx.setMouseState(true);\n          })(\"mouseleave\", function NzSubmenuNoneInlineChildComponent_mouseleave_HostBindingHandler() {\n            return ctx.setMouseState(false);\n          });\n        }\n        if (rf & 2) {\n          i0.ɵɵsyntheticHostProperty(\"@slideMotion\", ctx.expandState)(\"@zoomBigMotion\", ctx.expandState);\n          i0.ɵɵclassProp(\"ant-menu-light\", ctx.theme === \"light\")(\"ant-menu-dark\", ctx.theme === \"dark\")(\"ant-menu-submenu-placement-bottom\", ctx.mode === \"horizontal\")(\"ant-menu-submenu-placement-right\", ctx.mode === \"vertical\" && ctx.position === \"right\")(\"ant-menu-submenu-placement-left\", ctx.mode === \"vertical\" && ctx.position === \"left\")(\"ant-menu-submenu-rtl\", ctx.dir === \"rtl\");\n        }\n      },\n      inputs: {\n        menuClass: \"menuClass\",\n        theme: \"theme\",\n        templateOutlet: \"templateOutlet\",\n        isMenuInsideDropDown: \"isMenuInsideDropDown\",\n        mode: \"mode\",\n        position: \"position\",\n        nzDisabled: \"nzDisabled\",\n        nzOpen: \"nzOpen\"\n      },\n      outputs: {\n        subMenuMouseState: \"subMenuMouseState\"\n      },\n      exportAs: [\"nzSubmenuNoneInlineChild\"],\n      standalone: true,\n      features: [i0.ɵɵNgOnChangesFeature, i0.ɵɵStandaloneFeature],\n      attrs: _c3,\n      decls: 2,\n      vars: 16,\n      consts: [[3, \"ngClass\"], [3, \"ngTemplateOutlet\"]],\n      template: function NzSubmenuNoneInlineChildComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0);\n          i0.ɵɵtemplate(1, NzSubmenuNoneInlineChildComponent_ng_template_1_Template, 0, 0, \"ng-template\", 1);\n          i0.ɵɵelementEnd();\n        }\n        if (rf & 2) {\n          i0.ɵɵclassProp(\"ant-dropdown-menu\", ctx.isMenuInsideDropDown)(\"ant-menu\", !ctx.isMenuInsideDropDown)(\"ant-dropdown-menu-vertical\", ctx.isMenuInsideDropDown)(\"ant-menu-vertical\", !ctx.isMenuInsideDropDown)(\"ant-dropdown-menu-sub\", ctx.isMenuInsideDropDown)(\"ant-menu-sub\", !ctx.isMenuInsideDropDown)(\"ant-menu-rtl\", ctx.dir === \"rtl\");\n          i0.ɵɵproperty(\"ngClass\", ctx.menuClass);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngTemplateOutlet\", ctx.templateOutlet);\n        }\n      },\n      dependencies: [NgClass, NgTemplateOutlet],\n      encapsulation: 2,\n      data: {\n        animation: [zoomBigMotion, slideMotion]\n      },\n      changeDetection: 0\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(NzSubmenuNoneInlineChildComponent, [{\n    type: Component,\n    args: [{\n      selector: '[nz-submenu-none-inline-child]',\n      exportAs: 'nzSubmenuNoneInlineChild',\n      encapsulation: ViewEncapsulation.None,\n      animations: [zoomBigMotion, slideMotion],\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      template: `\n    <div\n      [class.ant-dropdown-menu]=\"isMenuInsideDropDown\"\n      [class.ant-menu]=\"!isMenuInsideDropDown\"\n      [class.ant-dropdown-menu-vertical]=\"isMenuInsideDropDown\"\n      [class.ant-menu-vertical]=\"!isMenuInsideDropDown\"\n      [class.ant-dropdown-menu-sub]=\"isMenuInsideDropDown\"\n      [class.ant-menu-sub]=\"!isMenuInsideDropDown\"\n      [class.ant-menu-rtl]=\"dir === 'rtl'\"\n      [ngClass]=\"menuClass\"\n    >\n      <ng-template [ngTemplateOutlet]=\"templateOutlet\"></ng-template>\n    </div>\n  `,\n      host: {\n        class: 'ant-menu-submenu ant-menu-submenu-popup',\n        '[class.ant-menu-light]': \"theme === 'light'\",\n        '[class.ant-menu-dark]': \"theme === 'dark'\",\n        '[class.ant-menu-submenu-placement-bottom]': \"mode === 'horizontal'\",\n        '[class.ant-menu-submenu-placement-right]': \"mode === 'vertical' && position === 'right'\",\n        '[class.ant-menu-submenu-placement-left]': \"mode === 'vertical' && position === 'left'\",\n        '[class.ant-menu-submenu-rtl]': 'dir ===\"rtl\"',\n        '[@slideMotion]': 'expandState',\n        '[@zoomBigMotion]': 'expandState',\n        '(mouseenter)': 'setMouseState(true)',\n        '(mouseleave)': 'setMouseState(false)'\n      },\n      imports: [NgClass, NgTemplateOutlet],\n      standalone: true\n    }]\n  }], () => [{\n    type: i1.Directionality,\n    decorators: [{\n      type: Optional\n    }]\n  }], {\n    menuClass: [{\n      type: Input\n    }],\n    theme: [{\n      type: Input\n    }],\n    templateOutlet: [{\n      type: Input\n    }],\n    isMenuInsideDropDown: [{\n      type: Input\n    }],\n    mode: [{\n      type: Input\n    }],\n    position: [{\n      type: Input\n    }],\n    nzDisabled: [{\n      type: Input\n    }],\n    nzOpen: [{\n      type: Input\n    }],\n    subMenuMouseState: [{\n      type: Output\n    }]\n  });\n})();\nclass NzSubMenuTitleComponent {\n  constructor(cdr, directionality) {\n    this.cdr = cdr;\n    this.directionality = directionality;\n    this.nzIcon = null;\n    this.nzTitle = null;\n    this.isMenuInsideDropDown = false;\n    this.nzDisabled = false;\n    this.paddingLeft = null;\n    this.mode = 'vertical';\n    this.toggleSubMenu = new EventEmitter();\n    this.subMenuMouseState = new EventEmitter();\n    this.dir = 'ltr';\n    this.destroy$ = new Subject();\n  }\n  ngOnInit() {\n    this.dir = this.directionality.value;\n    this.directionality.change?.pipe(takeUntil(this.destroy$)).subscribe(direction => {\n      this.dir = direction;\n      this.cdr.detectChanges();\n    });\n  }\n  ngOnDestroy() {\n    this.destroy$.next();\n    this.destroy$.complete();\n  }\n  setMouseState(state) {\n    if (!this.nzDisabled) {\n      this.subMenuMouseState.next(state);\n    }\n  }\n  clickTitle() {\n    if (this.mode === 'inline' && !this.nzDisabled) {\n      this.toggleSubMenu.emit();\n    }\n  }\n  static {\n    this.ɵfac = function NzSubMenuTitleComponent_Factory(t) {\n      return new (t || NzSubMenuTitleComponent)(i0.ɵɵdirectiveInject(i0.ChangeDetectorRef), i0.ɵɵdirectiveInject(i1.Directionality, 8));\n    };\n  }\n  static {\n    this.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n      type: NzSubMenuTitleComponent,\n      selectors: [[\"\", \"nz-submenu-title\", \"\"]],\n      hostVars: 8,\n      hostBindings: function NzSubMenuTitleComponent_HostBindings(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵlistener(\"click\", function NzSubMenuTitleComponent_click_HostBindingHandler() {\n            return ctx.clickTitle();\n          })(\"mouseenter\", function NzSubMenuTitleComponent_mouseenter_HostBindingHandler() {\n            return ctx.setMouseState(true);\n          })(\"mouseleave\", function NzSubMenuTitleComponent_mouseleave_HostBindingHandler() {\n            return ctx.setMouseState(false);\n          });\n        }\n        if (rf & 2) {\n          i0.ɵɵstyleProp(\"padding-left\", ctx.dir === \"rtl\" ? null : ctx.paddingLeft, \"px\")(\"padding-right\", ctx.dir === \"rtl\" ? ctx.paddingLeft : null, \"px\");\n          i0.ɵɵclassProp(\"ant-dropdown-menu-submenu-title\", ctx.isMenuInsideDropDown)(\"ant-menu-submenu-title\", !ctx.isMenuInsideDropDown);\n        }\n      },\n      inputs: {\n        nzIcon: \"nzIcon\",\n        nzTitle: \"nzTitle\",\n        isMenuInsideDropDown: \"isMenuInsideDropDown\",\n        nzDisabled: \"nzDisabled\",\n        paddingLeft: \"paddingLeft\",\n        mode: \"mode\"\n      },\n      outputs: {\n        toggleSubMenu: \"toggleSubMenu\",\n        subMenuMouseState: \"subMenuMouseState\"\n      },\n      exportAs: [\"nzSubmenuTitle\"],\n      standalone: true,\n      features: [i0.ɵɵStandaloneFeature],\n      attrs: _c4,\n      ngContentSelectors: _c1,\n      decls: 5,\n      vars: 3,\n      consts: [[\"nz-icon\", \"\", 3, \"nzType\"], [4, \"nzStringTemplateOutlet\"], [1, \"ant-dropdown-menu-submenu-expand-icon\"], [1, \"ant-menu-title-content\"], [\"nz-icon\", \"\", \"nzType\", \"left\", 1, \"ant-dropdown-menu-submenu-arrow-icon\"], [\"nz-icon\", \"\", \"nzType\", \"right\", 1, \"ant-dropdown-menu-submenu-arrow-icon\"], [1, \"ant-menu-submenu-arrow\"]],\n      template: function NzSubMenuTitleComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵprojectionDef();\n          i0.ɵɵtemplate(0, NzSubMenuTitleComponent_Conditional_0_Template, 1, 1, \"span\", 0)(1, NzSubMenuTitleComponent_ng_container_1_Template, 3, 1, \"ng-container\", 1);\n          i0.ɵɵprojection(2);\n          i0.ɵɵtemplate(3, NzSubMenuTitleComponent_Conditional_3_Template, 3, 1, \"span\", 2)(4, NzSubMenuTitleComponent_Conditional_4_Template, 1, 0);\n        }\n        if (rf & 2) {\n          i0.ɵɵconditional(0, ctx.nzIcon ? 0 : -1);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"nzStringTemplateOutlet\", ctx.nzTitle);\n          i0.ɵɵadvance(2);\n          i0.ɵɵconditional(3, ctx.isMenuInsideDropDown ? 3 : 4);\n        }\n      },\n      dependencies: [NzIconModule, i2.NzIconDirective, NzOutletModule, i3.NzStringTemplateOutletDirective],\n      encapsulation: 2,\n      changeDetection: 0\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(NzSubMenuTitleComponent, [{\n    type: Component,\n    args: [{\n      selector: '[nz-submenu-title]',\n      exportAs: 'nzSubmenuTitle',\n      encapsulation: ViewEncapsulation.None,\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      template: `\n    @if (nzIcon) {\n      <span nz-icon [nzType]=\"nzIcon\"></span>\n    }\n    <ng-container *nzStringTemplateOutlet=\"nzTitle\">\n      <span class=\"ant-menu-title-content\">{{ nzTitle }}</span>\n    </ng-container>\n    <ng-content />\n    @if (isMenuInsideDropDown) {\n      <span class=\"ant-dropdown-menu-submenu-expand-icon\">\n        @switch (dir) {\n          @case ('rtl') {\n            <span nz-icon nzType=\"left\" class=\"ant-dropdown-menu-submenu-arrow-icon\"></span>\n          }\n          @default {\n            <span nz-icon nzType=\"right\" class=\"ant-dropdown-menu-submenu-arrow-icon\"></span>\n          }\n        }\n      </span>\n    } @else {\n      <span class=\"ant-menu-submenu-arrow\"></span>\n    }\n  `,\n      host: {\n        '[class.ant-dropdown-menu-submenu-title]': 'isMenuInsideDropDown',\n        '[class.ant-menu-submenu-title]': '!isMenuInsideDropDown',\n        '[style.paddingLeft.px]': `dir === 'rtl' ? null : paddingLeft `,\n        '[style.paddingRight.px]': `dir === 'rtl' ? paddingLeft : null`,\n        '(click)': 'clickTitle()',\n        '(mouseenter)': 'setMouseState(true)',\n        '(mouseleave)': 'setMouseState(false)'\n      },\n      imports: [NzIconModule, NzOutletModule],\n      standalone: true\n    }]\n  }], () => [{\n    type: i0.ChangeDetectorRef\n  }, {\n    type: i1.Directionality,\n    decorators: [{\n      type: Optional\n    }]\n  }], {\n    nzIcon: [{\n      type: Input\n    }],\n    nzTitle: [{\n      type: Input\n    }],\n    isMenuInsideDropDown: [{\n      type: Input\n    }],\n    nzDisabled: [{\n      type: Input\n    }],\n    paddingLeft: [{\n      type: Input\n    }],\n    mode: [{\n      type: Input\n    }],\n    toggleSubMenu: [{\n      type: Output\n    }],\n    subMenuMouseState: [{\n      type: Output\n    }]\n  });\n})();\nconst listOfVerticalPositions = [POSITION_MAP.rightTop, POSITION_MAP.right, POSITION_MAP.rightBottom, POSITION_MAP.leftTop, POSITION_MAP.left, POSITION_MAP.leftBottom];\nconst listOfHorizontalPositions = [POSITION_MAP.bottomLeft, POSITION_MAP.bottomRight, POSITION_MAP.topRight, POSITION_MAP.topLeft];\nclass NzSubMenuComponent {\n  /** set the submenu host open status directly **/\n  setOpenStateWithoutDebounce(open) {\n    this.nzSubmenuService.setOpenStateWithoutDebounce(open);\n  }\n  toggleSubMenu() {\n    this.setOpenStateWithoutDebounce(!this.nzOpen);\n  }\n  setMouseEnterState(value) {\n    this.isActive = value;\n    if (this.mode !== 'inline') {\n      this.nzSubmenuService.setMouseEnterTitleOrOverlayState(value);\n    }\n  }\n  setTriggerWidth() {\n    if (this.mode === 'horizontal' && this.platform.isBrowser && this.cdkOverlayOrigin && this.nzPlacement === 'bottomLeft') {\n      /** TODO: fast dom **/\n      this.triggerWidth = this.cdkOverlayOrigin.nativeElement.getBoundingClientRect().width;\n    }\n  }\n  onPositionChange(position) {\n    const placement = getPlacementName(position);\n    if (placement === 'rightTop' || placement === 'rightBottom' || placement === 'right') {\n      this.position = 'right';\n    } else if (placement === 'leftTop' || placement === 'leftBottom' || placement === 'left') {\n      this.position = 'left';\n    }\n  }\n  constructor(nzMenuService, cdr, nzSubmenuService, platform, isMenuInsideDropDown, directionality, noAnimation) {\n    this.nzMenuService = nzMenuService;\n    this.cdr = cdr;\n    this.nzSubmenuService = nzSubmenuService;\n    this.platform = platform;\n    this.isMenuInsideDropDown = isMenuInsideDropDown;\n    this.directionality = directionality;\n    this.noAnimation = noAnimation;\n    this.nzMenuClassName = '';\n    this.nzPaddingLeft = null;\n    this.nzTitle = null;\n    this.nzIcon = null;\n    this.nzOpen = false;\n    this.nzDisabled = false;\n    this.nzPlacement = 'bottomLeft';\n    this.nzOpenChange = new EventEmitter();\n    this.cdkOverlayOrigin = null;\n    // fix errors about circular dependency\n    // Can't construct a query for the property ... since the query selector wasn't defined\"\n    this.listOfNzSubMenuComponent = null;\n    this.listOfNzMenuItemDirective = null;\n    this.level = this.nzSubmenuService.level;\n    this.destroy$ = new Subject();\n    this.position = 'right';\n    this.triggerWidth = null;\n    this.theme = 'light';\n    this.mode = 'vertical';\n    this.inlinePaddingLeft = null;\n    this.overlayPositions = listOfVerticalPositions;\n    this.isSelected = false;\n    this.isActive = false;\n    this.dir = 'ltr';\n  }\n  ngOnInit() {\n    /** submenu theme update **/\n    this.nzMenuService.theme$.pipe(takeUntil(this.destroy$)).subscribe(theme => {\n      this.theme = theme;\n      this.cdr.markForCheck();\n    });\n    /** submenu mode update **/\n    this.nzSubmenuService.mode$.pipe(takeUntil(this.destroy$)).subscribe(mode => {\n      this.mode = mode;\n      if (mode === 'horizontal') {\n        this.overlayPositions = [POSITION_MAP[this.nzPlacement], ...listOfHorizontalPositions];\n      } else if (mode === 'vertical') {\n        this.overlayPositions = listOfVerticalPositions;\n      }\n      this.cdr.markForCheck();\n    });\n    /** inlineIndent update **/\n    combineLatest([this.nzSubmenuService.mode$, this.nzMenuService.inlineIndent$]).pipe(takeUntil(this.destroy$)).subscribe(([mode, inlineIndent]) => {\n      this.inlinePaddingLeft = mode === 'inline' ? this.level * inlineIndent : null;\n      this.cdr.markForCheck();\n    });\n    /** current submenu open status **/\n    this.nzSubmenuService.isCurrentSubMenuOpen$.pipe(takeUntil(this.destroy$)).subscribe(open => {\n      this.isActive = open;\n      if (open !== this.nzOpen) {\n        this.setTriggerWidth();\n        this.nzOpen = open;\n        this.nzOpenChange.emit(this.nzOpen);\n        this.cdr.markForCheck();\n      }\n    });\n    this.dir = this.directionality.value;\n    this.directionality.change?.pipe(takeUntil(this.destroy$)).subscribe(direction => {\n      this.dir = direction;\n      this.cdr.markForCheck();\n    });\n  }\n  ngAfterContentInit() {\n    this.setTriggerWidth();\n    const listOfNzMenuItemDirective = this.listOfNzMenuItemDirective;\n    const changes = listOfNzMenuItemDirective.changes;\n    const mergedObservable = merge(...[changes, ...listOfNzMenuItemDirective.map(menu => menu.selected$)]);\n    changes.pipe(startWith(listOfNzMenuItemDirective), switchMap(() => mergedObservable), startWith(true), map(() => listOfNzMenuItemDirective.some(e => e.nzSelected)), takeUntil(this.destroy$)).subscribe(selected => {\n      this.isSelected = selected;\n      this.cdr.markForCheck();\n    });\n  }\n  ngOnChanges(changes) {\n    const {\n      nzOpen\n    } = changes;\n    if (nzOpen) {\n      this.nzSubmenuService.setOpenStateWithoutDebounce(this.nzOpen);\n      this.setTriggerWidth();\n    }\n  }\n  ngOnDestroy() {\n    this.destroy$.next();\n    this.destroy$.complete();\n  }\n  static {\n    this.ɵfac = function NzSubMenuComponent_Factory(t) {\n      return new (t || NzSubMenuComponent)(i0.ɵɵdirectiveInject(MenuService), i0.ɵɵdirectiveInject(i0.ChangeDetectorRef), i0.ɵɵdirectiveInject(NzSubmenuService), i0.ɵɵdirectiveInject(i3$1.Platform), i0.ɵɵdirectiveInject(NzIsMenuInsideDropDownToken), i0.ɵɵdirectiveInject(i1.Directionality, 8), i0.ɵɵdirectiveInject(i5.NzNoAnimationDirective, 9));\n    };\n  }\n  static {\n    this.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n      type: NzSubMenuComponent,\n      selectors: [[\"\", \"nz-submenu\", \"\"]],\n      contentQueries: function NzSubMenuComponent_ContentQueries(rf, ctx, dirIndex) {\n        if (rf & 1) {\n          i0.ɵɵcontentQuery(dirIndex, NzSubMenuComponent, 5);\n          i0.ɵɵcontentQuery(dirIndex, NzMenuItemComponent, 5);\n        }\n        if (rf & 2) {\n          let _t;\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.listOfNzSubMenuComponent = _t);\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.listOfNzMenuItemDirective = _t);\n        }\n      },\n      viewQuery: function NzSubMenuComponent_Query(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵviewQuery(CdkOverlayOrigin, 7, ElementRef);\n        }\n        if (rf & 2) {\n          let _t;\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.cdkOverlayOrigin = _t.first);\n        }\n      },\n      hostVars: 34,\n      hostBindings: function NzSubMenuComponent_HostBindings(rf, ctx) {\n        if (rf & 2) {\n          i0.ɵɵclassProp(\"ant-dropdown-menu-submenu\", ctx.isMenuInsideDropDown)(\"ant-dropdown-menu-submenu-disabled\", ctx.isMenuInsideDropDown && ctx.nzDisabled)(\"ant-dropdown-menu-submenu-open\", ctx.isMenuInsideDropDown && ctx.nzOpen)(\"ant-dropdown-menu-submenu-selected\", ctx.isMenuInsideDropDown && ctx.isSelected)(\"ant-dropdown-menu-submenu-vertical\", ctx.isMenuInsideDropDown && ctx.mode === \"vertical\")(\"ant-dropdown-menu-submenu-horizontal\", ctx.isMenuInsideDropDown && ctx.mode === \"horizontal\")(\"ant-dropdown-menu-submenu-inline\", ctx.isMenuInsideDropDown && ctx.mode === \"inline\")(\"ant-dropdown-menu-submenu-active\", ctx.isMenuInsideDropDown && ctx.isActive)(\"ant-menu-submenu\", !ctx.isMenuInsideDropDown)(\"ant-menu-submenu-disabled\", !ctx.isMenuInsideDropDown && ctx.nzDisabled)(\"ant-menu-submenu-open\", !ctx.isMenuInsideDropDown && ctx.nzOpen)(\"ant-menu-submenu-selected\", !ctx.isMenuInsideDropDown && ctx.isSelected)(\"ant-menu-submenu-vertical\", !ctx.isMenuInsideDropDown && ctx.mode === \"vertical\")(\"ant-menu-submenu-horizontal\", !ctx.isMenuInsideDropDown && ctx.mode === \"horizontal\")(\"ant-menu-submenu-inline\", !ctx.isMenuInsideDropDown && ctx.mode === \"inline\")(\"ant-menu-submenu-active\", !ctx.isMenuInsideDropDown && ctx.isActive)(\"ant-menu-submenu-rtl\", ctx.dir === \"rtl\");\n        }\n      },\n      inputs: {\n        nzMenuClassName: \"nzMenuClassName\",\n        nzPaddingLeft: \"nzPaddingLeft\",\n        nzTitle: \"nzTitle\",\n        nzIcon: \"nzIcon\",\n        nzOpen: \"nzOpen\",\n        nzDisabled: \"nzDisabled\",\n        nzPlacement: \"nzPlacement\"\n      },\n      outputs: {\n        nzOpenChange: \"nzOpenChange\"\n      },\n      exportAs: [\"nzSubmenu\"],\n      standalone: true,\n      features: [i0.ɵɵProvidersFeature([NzSubmenuService]), i0.ɵɵNgOnChangesFeature, i0.ɵɵStandaloneFeature],\n      attrs: _c5,\n      ngContentSelectors: _c7,\n      decls: 7,\n      vars: 8,\n      consts: [[\"origin\", \"cdkOverlayOrigin\"], [\"subMenuTemplate\", \"\"], [\"nz-submenu-title\", \"\", \"cdkOverlayOrigin\", \"\", 3, \"subMenuMouseState\", \"toggleSubMenu\", \"nzIcon\", \"nzTitle\", \"mode\", \"nzDisabled\", \"isMenuInsideDropDown\", \"paddingLeft\"], [\"nz-submenu-inline-child\", \"\", 3, \"mode\", \"nzOpen\", \"nzNoAnimation\", \"menuClass\", \"templateOutlet\"], [\"cdkConnectedOverlay\", \"\", 3, \"positionChange\", \"cdkConnectedOverlayPositions\", \"cdkConnectedOverlayOrigin\", \"cdkConnectedOverlayWidth\", \"cdkConnectedOverlayOpen\", \"cdkConnectedOverlayTransformOriginOn\"], [\"nz-submenu-none-inline-child\", \"\", 3, \"subMenuMouseState\", \"theme\", \"mode\", \"nzOpen\", \"position\", \"nzDisabled\", \"isMenuInsideDropDown\", \"templateOutlet\", \"menuClass\", \"nzNoAnimation\"]],\n      template: function NzSubMenuComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          const _r1 = i0.ɵɵgetCurrentView();\n          i0.ɵɵprojectionDef(_c6);\n          i0.ɵɵelementStart(0, \"div\", 2, 0);\n          i0.ɵɵlistener(\"subMenuMouseState\", function NzSubMenuComponent_Template_div_subMenuMouseState_0_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.setMouseEnterState($event));\n          })(\"toggleSubMenu\", function NzSubMenuComponent_Template_div_toggleSubMenu_0_listener() {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.toggleSubMenu());\n          });\n          i0.ɵɵtemplate(2, NzSubMenuComponent_Conditional_2_Template, 1, 0);\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(3, NzSubMenuComponent_Conditional_3_Template, 1, 6, \"div\", 3)(4, NzSubMenuComponent_Conditional_4_Template, 1, 5)(5, NzSubMenuComponent_ng_template_5_Template, 1, 0, \"ng-template\", null, 1, i0.ɵɵtemplateRefExtractor);\n        }\n        if (rf & 2) {\n          i0.ɵɵproperty(\"nzIcon\", ctx.nzIcon)(\"nzTitle\", ctx.nzTitle)(\"mode\", ctx.mode)(\"nzDisabled\", ctx.nzDisabled)(\"isMenuInsideDropDown\", ctx.isMenuInsideDropDown)(\"paddingLeft\", ctx.nzPaddingLeft || ctx.inlinePaddingLeft);\n          i0.ɵɵadvance(2);\n          i0.ɵɵconditional(2, !ctx.nzTitle ? 2 : -1);\n          i0.ɵɵadvance();\n          i0.ɵɵconditional(3, ctx.mode === \"inline\" ? 3 : 4);\n        }\n      },\n      dependencies: [NzSubMenuTitleComponent, NzSubmenuInlineChildComponent, NzNoAnimationDirective, NzSubmenuNoneInlineChildComponent, OverlayModule, i6.CdkConnectedOverlay, i6.CdkOverlayOrigin],\n      encapsulation: 2,\n      changeDetection: 0\n    });\n  }\n}\n__decorate([InputBoolean()], NzSubMenuComponent.prototype, \"nzOpen\", void 0);\n__decorate([InputBoolean()], NzSubMenuComponent.prototype, \"nzDisabled\", void 0);\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(NzSubMenuComponent, [{\n    type: Component,\n    args: [{\n      selector: '[nz-submenu]',\n      exportAs: 'nzSubmenu',\n      providers: [NzSubmenuService],\n      encapsulation: ViewEncapsulation.None,\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      preserveWhitespaces: false,\n      template: `\n    <div\n      nz-submenu-title\n      cdkOverlayOrigin\n      #origin=\"cdkOverlayOrigin\"\n      [nzIcon]=\"nzIcon\"\n      [nzTitle]=\"nzTitle\"\n      [mode]=\"mode\"\n      [nzDisabled]=\"nzDisabled\"\n      [isMenuInsideDropDown]=\"isMenuInsideDropDown\"\n      [paddingLeft]=\"nzPaddingLeft || inlinePaddingLeft\"\n      (subMenuMouseState)=\"setMouseEnterState($event)\"\n      (toggleSubMenu)=\"toggleSubMenu()\"\n    >\n      @if (!nzTitle) {\n        <ng-content select=\"[title]\" />\n      }\n    </div>\n    @if (mode === 'inline') {\n      <div\n        nz-submenu-inline-child\n        [mode]=\"mode\"\n        [nzOpen]=\"nzOpen\"\n        [@.disabled]=\"!!noAnimation?.nzNoAnimation\"\n        [nzNoAnimation]=\"noAnimation?.nzNoAnimation\"\n        [menuClass]=\"nzMenuClassName\"\n        [templateOutlet]=\"subMenuTemplate\"\n      ></div>\n    } @else {\n      <ng-template\n        cdkConnectedOverlay\n        (positionChange)=\"onPositionChange($event)\"\n        [cdkConnectedOverlayPositions]=\"overlayPositions\"\n        [cdkConnectedOverlayOrigin]=\"origin\"\n        [cdkConnectedOverlayWidth]=\"triggerWidth!\"\n        [cdkConnectedOverlayOpen]=\"nzOpen\"\n        [cdkConnectedOverlayTransformOriginOn]=\"'.ant-menu-submenu'\"\n      >\n        <div\n          nz-submenu-none-inline-child\n          [theme]=\"theme\"\n          [mode]=\"mode\"\n          [nzOpen]=\"nzOpen\"\n          [position]=\"position\"\n          [nzDisabled]=\"nzDisabled\"\n          [isMenuInsideDropDown]=\"isMenuInsideDropDown\"\n          [templateOutlet]=\"subMenuTemplate\"\n          [menuClass]=\"nzMenuClassName\"\n          [@.disabled]=\"!!noAnimation?.nzNoAnimation\"\n          [nzNoAnimation]=\"noAnimation?.nzNoAnimation\"\n          (subMenuMouseState)=\"setMouseEnterState($event)\"\n        ></div>\n      </ng-template>\n    }\n\n    <ng-template #subMenuTemplate>\n      <ng-content />\n    </ng-template>\n  `,\n      host: {\n        '[class.ant-dropdown-menu-submenu]': `isMenuInsideDropDown`,\n        '[class.ant-dropdown-menu-submenu-disabled]': `isMenuInsideDropDown && nzDisabled`,\n        '[class.ant-dropdown-menu-submenu-open]': `isMenuInsideDropDown && nzOpen`,\n        '[class.ant-dropdown-menu-submenu-selected]': `isMenuInsideDropDown && isSelected`,\n        '[class.ant-dropdown-menu-submenu-vertical]': `isMenuInsideDropDown && mode === 'vertical'`,\n        '[class.ant-dropdown-menu-submenu-horizontal]': `isMenuInsideDropDown && mode === 'horizontal'`,\n        '[class.ant-dropdown-menu-submenu-inline]': `isMenuInsideDropDown && mode === 'inline'`,\n        '[class.ant-dropdown-menu-submenu-active]': `isMenuInsideDropDown && isActive`,\n        '[class.ant-menu-submenu]': `!isMenuInsideDropDown`,\n        '[class.ant-menu-submenu-disabled]': `!isMenuInsideDropDown && nzDisabled`,\n        '[class.ant-menu-submenu-open]': `!isMenuInsideDropDown && nzOpen`,\n        '[class.ant-menu-submenu-selected]': `!isMenuInsideDropDown && isSelected`,\n        '[class.ant-menu-submenu-vertical]': `!isMenuInsideDropDown && mode === 'vertical'`,\n        '[class.ant-menu-submenu-horizontal]': `!isMenuInsideDropDown && mode === 'horizontal'`,\n        '[class.ant-menu-submenu-inline]': `!isMenuInsideDropDown && mode === 'inline'`,\n        '[class.ant-menu-submenu-active]': `!isMenuInsideDropDown && isActive`,\n        '[class.ant-menu-submenu-rtl]': `dir === 'rtl'`\n      },\n      imports: [NzSubMenuTitleComponent, NzSubmenuInlineChildComponent, NzNoAnimationDirective, NzSubmenuNoneInlineChildComponent, OverlayModule],\n      standalone: true\n    }]\n  }], () => [{\n    type: MenuService\n  }, {\n    type: i0.ChangeDetectorRef\n  }, {\n    type: NzSubmenuService\n  }, {\n    type: i3$1.Platform\n  }, {\n    type: undefined,\n    decorators: [{\n      type: Inject,\n      args: [NzIsMenuInsideDropDownToken]\n    }]\n  }, {\n    type: i1.Directionality,\n    decorators: [{\n      type: Optional\n    }]\n  }, {\n    type: i5.NzNoAnimationDirective,\n    decorators: [{\n      type: Host\n    }, {\n      type: Optional\n    }]\n  }], {\n    nzMenuClassName: [{\n      type: Input\n    }],\n    nzPaddingLeft: [{\n      type: Input\n    }],\n    nzTitle: [{\n      type: Input\n    }],\n    nzIcon: [{\n      type: Input\n    }],\n    nzOpen: [{\n      type: Input\n    }],\n    nzDisabled: [{\n      type: Input\n    }],\n    nzPlacement: [{\n      type: Input\n    }],\n    nzOpenChange: [{\n      type: Output\n    }],\n    cdkOverlayOrigin: [{\n      type: ViewChild,\n      args: [CdkOverlayOrigin, {\n        static: true,\n        read: ElementRef\n      }]\n    }],\n    listOfNzSubMenuComponent: [{\n      type: ContentChildren,\n      args: [forwardRef(() => NzSubMenuComponent), {\n        descendants: true\n      }]\n    }],\n    listOfNzMenuItemDirective: [{\n      type: ContentChildren,\n      args: [NzMenuItemComponent, {\n        descendants: true\n      }]\n    }]\n  });\n})();\nfunction MenuServiceFactory() {\n  const serviceInsideDropDown = inject(MenuService, {\n    skipSelf: true,\n    optional: true\n  });\n  const serviceOutsideDropDown = inject(NzMenuServiceLocalToken);\n  return serviceInsideDropDown ?? serviceOutsideDropDown;\n}\nfunction MenuDropDownTokenFactory() {\n  const isMenuInsideDropDownToken = inject(NzIsMenuInsideDropDownToken, {\n    skipSelf: true,\n    optional: true\n  });\n  return isMenuInsideDropDownToken ?? false;\n}\nclass NzMenuDirective {\n  setInlineCollapsed(inlineCollapsed) {\n    this.nzInlineCollapsed = inlineCollapsed;\n    this.inlineCollapsed$.next(inlineCollapsed);\n  }\n  updateInlineCollapse() {\n    if (this.listOfNzMenuItemDirective) {\n      if (this.nzInlineCollapsed) {\n        this.listOfOpenedNzSubMenuComponent = this.listOfNzSubMenuComponent.filter(submenu => submenu.nzOpen);\n        this.listOfNzSubMenuComponent.forEach(submenu => submenu.setOpenStateWithoutDebounce(false));\n      } else {\n        this.listOfOpenedNzSubMenuComponent.forEach(submenu => submenu.setOpenStateWithoutDebounce(true));\n        this.listOfOpenedNzSubMenuComponent = [];\n      }\n    }\n  }\n  constructor(nzMenuService, isMenuInsideDropDown, cdr, directionality) {\n    this.nzMenuService = nzMenuService;\n    this.isMenuInsideDropDown = isMenuInsideDropDown;\n    this.cdr = cdr;\n    this.directionality = directionality;\n    this.nzInlineIndent = 24;\n    this.nzTheme = 'light';\n    this.nzMode = 'vertical';\n    this.nzInlineCollapsed = false;\n    this.nzSelectable = !this.isMenuInsideDropDown;\n    this.nzClick = new EventEmitter();\n    this.actualMode = 'vertical';\n    this.dir = 'ltr';\n    this.inlineCollapsed$ = new BehaviorSubject(this.nzInlineCollapsed);\n    this.mode$ = new BehaviorSubject(this.nzMode);\n    this.destroy$ = new Subject();\n    this.listOfOpenedNzSubMenuComponent = [];\n  }\n  ngOnInit() {\n    combineLatest([this.inlineCollapsed$, this.mode$]).pipe(takeUntil(this.destroy$)).subscribe(([inlineCollapsed, mode]) => {\n      this.actualMode = inlineCollapsed ? 'vertical' : mode;\n      this.nzMenuService.setMode(this.actualMode);\n      this.cdr.markForCheck();\n    });\n    this.nzMenuService.descendantMenuItemClick$.pipe(takeUntil(this.destroy$)).subscribe(menu => {\n      this.nzClick.emit(menu);\n      if (this.nzSelectable && !menu.nzMatchRouter) {\n        this.listOfNzMenuItemDirective.forEach(item => item.setSelectedState(item === menu));\n      }\n    });\n    this.dir = this.directionality.value;\n    this.directionality.change?.pipe(takeUntil(this.destroy$)).subscribe(direction => {\n      this.dir = direction;\n      this.nzMenuService.setMode(this.actualMode);\n      this.cdr.markForCheck();\n    });\n  }\n  ngAfterContentInit() {\n    this.inlineCollapsed$.pipe(takeUntil(this.destroy$)).subscribe(() => {\n      this.updateInlineCollapse();\n      this.cdr.markForCheck();\n    });\n  }\n  ngOnChanges(changes) {\n    const {\n      nzInlineCollapsed,\n      nzInlineIndent,\n      nzTheme,\n      nzMode\n    } = changes;\n    if (nzInlineCollapsed) {\n      this.inlineCollapsed$.next(this.nzInlineCollapsed);\n    }\n    if (nzInlineIndent) {\n      this.nzMenuService.setInlineIndent(this.nzInlineIndent);\n    }\n    if (nzTheme) {\n      this.nzMenuService.setTheme(this.nzTheme);\n    }\n    if (nzMode) {\n      this.mode$.next(this.nzMode);\n      if (!changes.nzMode.isFirstChange() && this.listOfNzSubMenuComponent) {\n        this.listOfNzSubMenuComponent.forEach(submenu => submenu.setOpenStateWithoutDebounce(false));\n      }\n    }\n  }\n  ngOnDestroy() {\n    this.destroy$.next(true);\n    this.destroy$.complete();\n  }\n  static {\n    this.ɵfac = function NzMenuDirective_Factory(t) {\n      return new (t || NzMenuDirective)(i0.ɵɵdirectiveInject(MenuService), i0.ɵɵdirectiveInject(NzIsMenuInsideDropDownToken), i0.ɵɵdirectiveInject(i0.ChangeDetectorRef), i0.ɵɵdirectiveInject(i1.Directionality, 8));\n    };\n  }\n  static {\n    this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: NzMenuDirective,\n      selectors: [[\"\", \"nz-menu\", \"\"]],\n      contentQueries: function NzMenuDirective_ContentQueries(rf, ctx, dirIndex) {\n        if (rf & 1) {\n          i0.ɵɵcontentQuery(dirIndex, NzMenuItemComponent, 5);\n          i0.ɵɵcontentQuery(dirIndex, NzSubMenuComponent, 5);\n        }\n        if (rf & 2) {\n          let _t;\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.listOfNzMenuItemDirective = _t);\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.listOfNzSubMenuComponent = _t);\n        }\n      },\n      hostVars: 34,\n      hostBindings: function NzMenuDirective_HostBindings(rf, ctx) {\n        if (rf & 2) {\n          i0.ɵɵclassProp(\"ant-dropdown-menu\", ctx.isMenuInsideDropDown)(\"ant-dropdown-menu-root\", ctx.isMenuInsideDropDown)(\"ant-dropdown-menu-light\", ctx.isMenuInsideDropDown && ctx.nzTheme === \"light\")(\"ant-dropdown-menu-dark\", ctx.isMenuInsideDropDown && ctx.nzTheme === \"dark\")(\"ant-dropdown-menu-vertical\", ctx.isMenuInsideDropDown && ctx.actualMode === \"vertical\")(\"ant-dropdown-menu-horizontal\", ctx.isMenuInsideDropDown && ctx.actualMode === \"horizontal\")(\"ant-dropdown-menu-inline\", ctx.isMenuInsideDropDown && ctx.actualMode === \"inline\")(\"ant-dropdown-menu-inline-collapsed\", ctx.isMenuInsideDropDown && ctx.nzInlineCollapsed)(\"ant-menu\", !ctx.isMenuInsideDropDown)(\"ant-menu-root\", !ctx.isMenuInsideDropDown)(\"ant-menu-light\", !ctx.isMenuInsideDropDown && ctx.nzTheme === \"light\")(\"ant-menu-dark\", !ctx.isMenuInsideDropDown && ctx.nzTheme === \"dark\")(\"ant-menu-vertical\", !ctx.isMenuInsideDropDown && ctx.actualMode === \"vertical\")(\"ant-menu-horizontal\", !ctx.isMenuInsideDropDown && ctx.actualMode === \"horizontal\")(\"ant-menu-inline\", !ctx.isMenuInsideDropDown && ctx.actualMode === \"inline\")(\"ant-menu-inline-collapsed\", !ctx.isMenuInsideDropDown && ctx.nzInlineCollapsed)(\"ant-menu-rtl\", ctx.dir === \"rtl\");\n        }\n      },\n      inputs: {\n        nzInlineIndent: \"nzInlineIndent\",\n        nzTheme: \"nzTheme\",\n        nzMode: \"nzMode\",\n        nzInlineCollapsed: \"nzInlineCollapsed\",\n        nzSelectable: \"nzSelectable\"\n      },\n      outputs: {\n        nzClick: \"nzClick\"\n      },\n      exportAs: [\"nzMenu\"],\n      standalone: true,\n      features: [i0.ɵɵProvidersFeature([{\n        provide: NzMenuServiceLocalToken,\n        useClass: MenuService\n      }, /** use the top level service **/\n      {\n        provide: MenuService,\n        useFactory: MenuServiceFactory\n      }, /** check if menu inside dropdown-menu component **/\n      {\n        provide: NzIsMenuInsideDropDownToken,\n        useFactory: MenuDropDownTokenFactory\n      }]), i0.ɵɵNgOnChangesFeature]\n    });\n  }\n}\n__decorate([InputBoolean()], NzMenuDirective.prototype, \"nzInlineCollapsed\", void 0);\n__decorate([InputBoolean()], NzMenuDirective.prototype, \"nzSelectable\", void 0);\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(NzMenuDirective, [{\n    type: Directive,\n    args: [{\n      selector: '[nz-menu]',\n      exportAs: 'nzMenu',\n      providers: [{\n        provide: NzMenuServiceLocalToken,\n        useClass: MenuService\n      }, /** use the top level service **/\n      {\n        provide: MenuService,\n        useFactory: MenuServiceFactory\n      }, /** check if menu inside dropdown-menu component **/\n      {\n        provide: NzIsMenuInsideDropDownToken,\n        useFactory: MenuDropDownTokenFactory\n      }],\n      host: {\n        '[class.ant-dropdown-menu]': `isMenuInsideDropDown`,\n        '[class.ant-dropdown-menu-root]': `isMenuInsideDropDown`,\n        '[class.ant-dropdown-menu-light]': `isMenuInsideDropDown && nzTheme === 'light'`,\n        '[class.ant-dropdown-menu-dark]': `isMenuInsideDropDown && nzTheme === 'dark'`,\n        '[class.ant-dropdown-menu-vertical]': `isMenuInsideDropDown && actualMode === 'vertical'`,\n        '[class.ant-dropdown-menu-horizontal]': `isMenuInsideDropDown && actualMode === 'horizontal'`,\n        '[class.ant-dropdown-menu-inline]': `isMenuInsideDropDown && actualMode === 'inline'`,\n        '[class.ant-dropdown-menu-inline-collapsed]': `isMenuInsideDropDown && nzInlineCollapsed`,\n        '[class.ant-menu]': `!isMenuInsideDropDown`,\n        '[class.ant-menu-root]': `!isMenuInsideDropDown`,\n        '[class.ant-menu-light]': `!isMenuInsideDropDown && nzTheme === 'light'`,\n        '[class.ant-menu-dark]': `!isMenuInsideDropDown && nzTheme === 'dark'`,\n        '[class.ant-menu-vertical]': `!isMenuInsideDropDown && actualMode === 'vertical'`,\n        '[class.ant-menu-horizontal]': `!isMenuInsideDropDown && actualMode === 'horizontal'`,\n        '[class.ant-menu-inline]': `!isMenuInsideDropDown && actualMode === 'inline'`,\n        '[class.ant-menu-inline-collapsed]': `!isMenuInsideDropDown && nzInlineCollapsed`,\n        '[class.ant-menu-rtl]': `dir === 'rtl'`\n      },\n      standalone: true\n    }]\n  }], () => [{\n    type: MenuService\n  }, {\n    type: undefined,\n    decorators: [{\n      type: Inject,\n      args: [NzIsMenuInsideDropDownToken]\n    }]\n  }, {\n    type: i0.ChangeDetectorRef\n  }, {\n    type: i1.Directionality,\n    decorators: [{\n      type: Optional\n    }]\n  }], {\n    listOfNzMenuItemDirective: [{\n      type: ContentChildren,\n      args: [NzMenuItemComponent, {\n        descendants: true\n      }]\n    }],\n    listOfNzSubMenuComponent: [{\n      type: ContentChildren,\n      args: [NzSubMenuComponent, {\n        descendants: true\n      }]\n    }],\n    nzInlineIndent: [{\n      type: Input\n    }],\n    nzTheme: [{\n      type: Input\n    }],\n    nzMode: [{\n      type: Input\n    }],\n    nzInlineCollapsed: [{\n      type: Input\n    }],\n    nzSelectable: [{\n      type: Input\n    }],\n    nzClick: [{\n      type: Output\n    }]\n  });\n})();\n\n/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\nfunction MenuGroupFactory() {\n  const isMenuInsideDropDownToken = inject(NzIsMenuInsideDropDownToken, {\n    optional: true,\n    skipSelf: true\n  });\n  return isMenuInsideDropDownToken ?? false;\n}\nclass NzMenuGroupComponent {\n  constructor(elementRef, renderer, isMenuInsideDropDown) {\n    this.elementRef = elementRef;\n    this.renderer = renderer;\n    this.isMenuInsideDropDown = isMenuInsideDropDown;\n    const className = this.isMenuInsideDropDown ? 'ant-dropdown-menu-item-group' : 'ant-menu-item-group';\n    this.renderer.addClass(elementRef.nativeElement, className);\n  }\n  ngAfterViewInit() {\n    const ulElement = this.titleElement.nativeElement.nextElementSibling;\n    if (ulElement) {\n      /** add classname to ul **/\n      const className = this.isMenuInsideDropDown ? 'ant-dropdown-menu-item-group-list' : 'ant-menu-item-group-list';\n      this.renderer.addClass(ulElement, className);\n    }\n  }\n  static {\n    this.ɵfac = function NzMenuGroupComponent_Factory(t) {\n      return new (t || NzMenuGroupComponent)(i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(i0.Renderer2), i0.ɵɵdirectiveInject(NzIsMenuInsideDropDownToken));\n    };\n  }\n  static {\n    this.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n      type: NzMenuGroupComponent,\n      selectors: [[\"\", \"nz-menu-group\", \"\"]],\n      viewQuery: function NzMenuGroupComponent_Query(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵviewQuery(_c8, 5);\n        }\n        if (rf & 2) {\n          let _t;\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.titleElement = _t.first);\n        }\n      },\n      inputs: {\n        nzTitle: \"nzTitle\"\n      },\n      exportAs: [\"nzMenuGroup\"],\n      standalone: true,\n      features: [i0.ɵɵProvidersFeature([/** check if menu inside dropdown-menu component **/\n      {\n        provide: NzIsMenuInsideDropDownToken,\n        useFactory: MenuGroupFactory\n      }]), i0.ɵɵStandaloneFeature],\n      attrs: _c9,\n      ngContentSelectors: _c11,\n      decls: 5,\n      vars: 6,\n      consts: [[\"titleElement\", \"\"], [4, \"nzStringTemplateOutlet\"]],\n      template: function NzMenuGroupComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵprojectionDef(_c10);\n          i0.ɵɵelementStart(0, \"div\", null, 0);\n          i0.ɵɵtemplate(2, NzMenuGroupComponent_ng_container_2_Template, 2, 1, \"ng-container\", 1)(3, NzMenuGroupComponent_Conditional_3_Template, 1, 0);\n          i0.ɵɵelementEnd();\n          i0.ɵɵprojection(4);\n        }\n        if (rf & 2) {\n          i0.ɵɵclassProp(\"ant-menu-item-group-title\", !ctx.isMenuInsideDropDown)(\"ant-dropdown-menu-item-group-title\", ctx.isMenuInsideDropDown);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"nzStringTemplateOutlet\", ctx.nzTitle);\n          i0.ɵɵadvance();\n          i0.ɵɵconditional(3, !ctx.nzTitle ? 3 : -1);\n        }\n      },\n      dependencies: [NzOutletModule, i3.NzStringTemplateOutletDirective],\n      encapsulation: 2,\n      changeDetection: 0\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(NzMenuGroupComponent, [{\n    type: Component,\n    args: [{\n      selector: '[nz-menu-group]',\n      exportAs: 'nzMenuGroup',\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      providers: [/** check if menu inside dropdown-menu component **/\n      {\n        provide: NzIsMenuInsideDropDownToken,\n        useFactory: MenuGroupFactory\n      }],\n      encapsulation: ViewEncapsulation.None,\n      template: `\n    <div\n      [class.ant-menu-item-group-title]=\"!isMenuInsideDropDown\"\n      [class.ant-dropdown-menu-item-group-title]=\"isMenuInsideDropDown\"\n      #titleElement\n    >\n      <ng-container *nzStringTemplateOutlet=\"nzTitle\">{{ nzTitle }}</ng-container>\n      @if (!nzTitle) {\n        <ng-content select=\"[title]\" />\n      }\n    </div>\n    <ng-content></ng-content>\n  `,\n      preserveWhitespaces: false,\n      imports: [NzOutletModule],\n      standalone: true\n    }]\n  }], () => [{\n    type: i0.ElementRef\n  }, {\n    type: i0.Renderer2\n  }, {\n    type: undefined,\n    decorators: [{\n      type: Inject,\n      args: [NzIsMenuInsideDropDownToken]\n    }]\n  }], {\n    nzTitle: [{\n      type: Input\n    }],\n    titleElement: [{\n      type: ViewChild,\n      args: ['titleElement']\n    }]\n  });\n})();\n\n/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\nclass NzMenuDividerDirective {\n  constructor(elementRef) {\n    this.elementRef = elementRef;\n  }\n  static {\n    this.ɵfac = function NzMenuDividerDirective_Factory(t) {\n      return new (t || NzMenuDividerDirective)(i0.ɵɵdirectiveInject(i0.ElementRef));\n    };\n  }\n  static {\n    this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: NzMenuDividerDirective,\n      selectors: [[\"\", \"nz-menu-divider\", \"\"]],\n      hostAttrs: [1, \"ant-dropdown-menu-item-divider\"],\n      exportAs: [\"nzMenuDivider\"],\n      standalone: true\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(NzMenuDividerDirective, [{\n    type: Directive,\n    args: [{\n      selector: '[nz-menu-divider]',\n      exportAs: 'nzMenuDivider',\n      host: {\n        class: 'ant-dropdown-menu-item-divider'\n      },\n      standalone: true\n    }]\n  }], () => [{\n    type: i0.ElementRef\n  }], null);\n})();\n\n/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\nclass NzMenuModule {\n  static {\n    this.ɵfac = function NzMenuModule_Factory(t) {\n      return new (t || NzMenuModule)();\n    };\n  }\n  static {\n    this.ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n      type: NzMenuModule,\n      imports: [NzMenuDirective, NzMenuItemComponent, NzSubMenuComponent, NzMenuDividerDirective, NzMenuGroupComponent, NzSubMenuTitleComponent, NzSubmenuInlineChildComponent, NzSubmenuNoneInlineChildComponent],\n      exports: [NzMenuDirective, NzMenuItemComponent, NzSubMenuComponent, NzMenuDividerDirective, NzMenuGroupComponent]\n    });\n  }\n  static {\n    this.ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n      imports: [NzSubMenuComponent, NzMenuGroupComponent, NzSubMenuTitleComponent]\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(NzMenuModule, [{\n    type: NgModule,\n    args: [{\n      imports: [NzMenuDirective, NzMenuItemComponent, NzSubMenuComponent, NzMenuDividerDirective, NzMenuGroupComponent, NzSubMenuTitleComponent, NzSubmenuInlineChildComponent, NzSubmenuNoneInlineChildComponent],\n      exports: [NzMenuDirective, NzMenuItemComponent, NzSubMenuComponent, NzMenuDividerDirective, NzMenuGroupComponent]\n    }]\n  }], null, null);\n})();\n\n/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\n\n/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { MenuDropDownTokenFactory, MenuGroupFactory, MenuService, MenuServiceFactory, NzIsMenuInsideDropDownToken, NzMenuDirective, NzMenuDividerDirective, NzMenuGroupComponent, NzMenuItemComponent, NzMenuModule, NzMenuServiceLocalToken, NzSubMenuComponent, NzSubMenuTitleComponent, NzSubmenuInlineChildComponent, NzSubmenuNoneInlineChildComponent, NzSubmenuService };\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA0BA,IAAM,MAAM,CAAC,gBAAgB,EAAE;AAC/B,IAAM,MAAM,CAAC,GAAG;AAChB,IAAM,MAAM,CAAC,2BAA2B,EAAE;AAC1C,SAAS,qDAAqD,IAAI,KAAK;AAAC;AACxE,IAAM,MAAM,CAAC,gCAAgC,EAAE;AAC/C,SAAS,yDAAyD,IAAI,KAAK;AAAC;AAC5E,IAAM,MAAM,CAAC,oBAAoB,EAAE;AACnC,SAAS,+CAA+C,IAAI,KAAK;AAC/D,MAAI,KAAK,GAAG;AACV,IAAG,UAAU,GAAG,QAAQ,CAAC;AAAA,EAC3B;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc;AAChC,IAAG,WAAW,UAAU,OAAO,MAAM;AAAA,EACvC;AACF;AACA,SAAS,gDAAgD,IAAI,KAAK;AAChE,MAAI,KAAK,GAAG;AACV,IAAG,wBAAwB,CAAC;AAC5B,IAAG,eAAe,GAAG,QAAQ,CAAC;AAC9B,IAAG,OAAO,CAAC;AACX,IAAG,aAAa;AAChB,IAAG,sBAAsB;AAAA,EAC3B;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc;AAChC,IAAG,UAAU,CAAC;AACd,IAAG,kBAAkB,OAAO,OAAO;AAAA,EACrC;AACF;AACA,SAAS,sDAAsD,IAAI,KAAK;AACtE,MAAI,KAAK,GAAG;AACV,IAAG,UAAU,GAAG,QAAQ,CAAC;AAAA,EAC3B;AACF;AACA,SAAS,sDAAsD,IAAI,KAAK;AACtE,MAAI,KAAK,GAAG;AACV,IAAG,UAAU,GAAG,QAAQ,CAAC;AAAA,EAC3B;AACF;AACA,SAAS,+CAA+C,IAAI,KAAK;AAC/D,MAAI,KAAK,GAAG;AACV,IAAG,eAAe,GAAG,QAAQ,CAAC;AAC9B,IAAG,WAAW,GAAG,uDAAuD,GAAG,CAAC,EAAE,GAAG,uDAAuD,GAAG,CAAC;AAC5I,IAAG,aAAa;AAAA,EAClB;AACA,MAAI,KAAK,GAAG;AACV,QAAI;AACJ,UAAM,SAAY,cAAc;AAChC,IAAG,UAAU;AACb,IAAG,cAAc,IAAI,UAAU,OAAO,SAAS,QAAQ,IAAI,CAAC;AAAA,EAC9D;AACF;AACA,SAAS,+CAA+C,IAAI,KAAK;AAC/D,MAAI,KAAK,GAAG;AACV,IAAG,UAAU,GAAG,QAAQ,CAAC;AAAA,EAC3B;AACF;AACA,IAAM,MAAM,CAAC,cAAc,EAAE;AAC7B,IAAM,MAAM,CAAC,CAAC,CAAC,IAAI,SAAS,EAAE,CAAC,GAAG,GAAG;AACrC,IAAM,MAAM,CAAC,WAAW,GAAG;AAC3B,SAAS,0CAA0C,IAAI,KAAK;AAC1D,MAAI,KAAK,GAAG;AACV,IAAG,aAAa,CAAC;AAAA,EACnB;AACF;AACA,SAAS,0CAA0C,IAAI,KAAK;AAC1D,MAAI,KAAK,GAAG;AACV,IAAG,UAAU,GAAG,OAAO,CAAC;AAAA,EAC1B;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc;AAChC,UAAM,qBAAwB,YAAY,CAAC;AAC3C,IAAG,WAAW,QAAQ,OAAO,IAAI,EAAE,UAAU,OAAO,MAAM,EAAE,cAAc,CAAC,EAAE,OAAO,eAAe,OAAO,OAAO,OAAO,YAAY,cAAc,EAAE,iBAAiB,OAAO,eAAe,OAAO,OAAO,OAAO,YAAY,aAAa,EAAE,aAAa,OAAO,eAAe,EAAE,kBAAkB,kBAAkB;AAAA,EACtT;AACF;AACA,SAAS,wDAAwD,IAAI,KAAK;AACxE,MAAI,KAAK,GAAG;AACV,UAAM,MAAS,iBAAiB;AAChC,IAAG,eAAe,GAAG,OAAO,CAAC;AAC7B,IAAG,WAAW,qBAAqB,SAAS,yFAAyF,QAAQ;AAC3I,MAAG,cAAc,GAAG;AACpB,YAAM,SAAY,cAAc,CAAC;AACjC,aAAU,YAAY,OAAO,mBAAmB,MAAM,CAAC;AAAA,IACzD,CAAC;AACD,IAAG,aAAa;AAAA,EAClB;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc,CAAC;AACjC,UAAM,qBAAwB,YAAY,CAAC;AAC3C,IAAG,WAAW,SAAS,OAAO,KAAK,EAAE,QAAQ,OAAO,IAAI,EAAE,UAAU,OAAO,MAAM,EAAE,YAAY,OAAO,QAAQ,EAAE,cAAc,OAAO,UAAU,EAAE,wBAAwB,OAAO,oBAAoB,EAAE,kBAAkB,kBAAkB,EAAE,aAAa,OAAO,eAAe,EAAE,cAAc,CAAC,EAAE,OAAO,eAAe,OAAO,OAAO,OAAO,YAAY,cAAc,EAAE,iBAAiB,OAAO,eAAe,OAAO,OAAO,OAAO,YAAY,aAAa;AAAA,EAChc;AACF;AACA,SAAS,0CAA0C,IAAI,KAAK;AAC1D,MAAI,KAAK,GAAG;AACV,UAAM,MAAS,iBAAiB;AAChC,IAAG,WAAW,GAAG,yDAAyD,GAAG,IAAI,eAAe,CAAC;AACjG,IAAG,WAAW,kBAAkB,SAAS,gFAAgF,QAAQ;AAC/H,MAAG,cAAc,GAAG;AACpB,YAAM,SAAY,cAAc;AAChC,aAAU,YAAY,OAAO,iBAAiB,MAAM,CAAC;AAAA,IACvD,CAAC;AAAA,EACH;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc;AAChC,UAAM,YAAe,YAAY,CAAC;AAClC,IAAG,WAAW,gCAAgC,OAAO,gBAAgB,EAAE,6BAA6B,SAAS,EAAE,4BAA4B,OAAO,YAAY,EAAE,2BAA2B,OAAO,MAAM,EAAE,wCAAwC,mBAAmB;AAAA,EACvQ;AACF;AACA,SAAS,0CAA0C,IAAI,KAAK;AAC1D,MAAI,KAAK,GAAG;AACV,IAAG,aAAa,GAAG,CAAC;AAAA,EACtB;AACF;AACA,IAAM,MAAM,CAAC,cAAc;AAC3B,IAAM,MAAM,CAAC,iBAAiB,EAAE;AAChC,IAAM,OAAO,CAAC,KAAK,CAAC,CAAC,IAAI,SAAS,EAAE,CAAC,CAAC;AACtC,IAAM,OAAO,CAAC,KAAK,SAAS;AAC5B,SAAS,6CAA6C,IAAI,KAAK;AAC7D,MAAI,KAAK,GAAG;AACV,IAAG,wBAAwB,CAAC;AAC5B,IAAG,OAAO,CAAC;AACX,IAAG,sBAAsB;AAAA,EAC3B;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc;AAChC,IAAG,UAAU;AACb,IAAG,kBAAkB,OAAO,OAAO;AAAA,EACrC;AACF;AACA,SAAS,4CAA4C,IAAI,KAAK;AAC5D,MAAI,KAAK,GAAG;AACV,IAAG,aAAa,GAAG,CAAC;AAAA,EACtB;AACF;AACA,IAAM,8BAA8B,IAAI,eAAe,yBAAyB;AAChF,IAAM,0BAA0B,IAAI,eAAe,yBAAyB;AAM5E,IAAM,cAAN,MAAM,aAAY;AAAA,EAChB,cAAc;AAEZ,SAAK,2BAA2B,IAAI,QAAQ;AAE5C,SAAK,sBAAsB,IAAI,QAAQ;AACvC,SAAK,SAAS,IAAI,gBAAgB,OAAO;AACzC,SAAK,QAAQ,IAAI,gBAAgB,UAAU;AAC3C,SAAK,gBAAgB,IAAI,gBAAgB,EAAE;AAC3C,SAAK,sBAAsB,IAAI,gBAAgB,KAAK;AAAA,EACtD;AAAA,EACA,0BAA0B,MAAM;AAC9B,SAAK,yBAAyB,KAAK,IAAI;AAAA,EACzC;AAAA,EACA,qBAAqB,MAAM;AACzB,SAAK,oBAAoB,KAAK,IAAI;AAAA,EACpC;AAAA,EACA,QAAQ,MAAM;AACZ,SAAK,MAAM,KAAK,IAAI;AAAA,EACtB;AAAA,EACA,SAAS,OAAO;AACd,SAAK,OAAO,KAAK,KAAK;AAAA,EACxB;AAAA,EACA,gBAAgB,QAAQ;AACtB,SAAK,cAAc,KAAK,MAAM;AAAA,EAChC;AAAA,EACA,OAAO;AACL,SAAK,OAAO,SAAS,oBAAoB,GAAG;AAC1C,aAAO,KAAK,KAAK,cAAa;AAAA,IAChC;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,QAA0B,mBAAmB;AAAA,MAChD,OAAO;AAAA,MACP,SAAS,aAAY;AAAA,IACvB,CAAC;AAAA,EACH;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,aAAa,CAAC;AAAA,IACpF,MAAM;AAAA,EACR,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;AAMH,IAAM,mBAAN,MAAM,kBAAiB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMrB,qBAAqB,MAAM;AACzB,SAAK,oBAAoB,KAAK,IAAI;AAAA,EACpC;AAAA,EACA,4BAA4B,OAAO;AACjC,SAAK,sBAAsB,KAAK,KAAK;AAAA,EACvC;AAAA,EACA,iCAAiC,OAAO;AACtC,SAAK,4BAA4B,KAAK,KAAK;AAAA,EAC7C;AAAA,EACA,YAAY,sBAAsB,eAAe,sBAAsB;AACrE,SAAK,uBAAuB;AAC5B,SAAK,gBAAgB;AACrB,SAAK,uBAAuB;AAC5B,SAAK,QAAQ,KAAK,cAAc,MAAM,KAAK,IAAI,UAAQ;AACrD,UAAI,SAAS,UAAU;AACrB,eAAO;AAAA,MAET,WAAW,SAAS,cAAc,KAAK,sBAAsB;AAC3D,eAAO;AAAA,MACT,OAAO;AACL,eAAO;AAAA,MACT;AAAA,IACF,CAAC,CAAC;AACF,SAAK,QAAQ;AACb,SAAK,wBAAwB,IAAI,gBAAgB,KAAK;AACtD,SAAK,sBAAsB,IAAI,gBAAgB,KAAK;AAEpD,SAAK,8BAA8B,IAAI,QAAQ;AAC/C,SAAK,sBAAsB,IAAI,QAAQ;AACvC,SAAK,WAAW,IAAI,QAAQ;AAC5B,QAAI,KAAK,sBAAsB;AAC7B,WAAK,QAAQ,KAAK,qBAAqB,QAAQ;AAAA,IACjD;AAEA,UAAM,0BAA0B,KAAK,oBAAoB,KAAK,SAAS,MAAM,KAAK,KAAK,GAAG,OAAO,UAAQ,SAAS,YAAY,KAAK,oBAAoB,GAAG,MAAM,KAAK,CAAC;AACtK,UAAM,wBAAwB,MAAM,KAAK,6BAA6B,uBAAuB;AAE7F,UAAM,6BAA6B,cAAc,CAAC,KAAK,qBAAqB,qBAAqB,CAAC,EAAE,KAAK,IAAI,CAAC,CAAC,oBAAoB,oBAAoB,MAAM,sBAAsB,oBAAoB,GAAG,UAAU,GAAG,GAAG,qBAAqB,GAAG,UAAU,KAAK,QAAQ,CAAC;AAC1Q,+BAA2B,KAAK,qBAAqB,CAAC,EAAE,UAAU,UAAQ;AACxE,WAAK,4BAA4B,IAAI;AACrC,UAAI,KAAK,sBAAsB;AAE7B,aAAK,qBAAqB,oBAAoB,KAAK,IAAI;AAAA,MACzD,OAAO;AACL,aAAK,cAAc,oBAAoB,KAAK,IAAI;AAAA,MAClD;AAAA,IACF,CAAC;AAAA,EACH;AAAA,EACA,cAAc;AACZ,SAAK,SAAS,KAAK;AACnB,SAAK,SAAS,SAAS;AAAA,EACzB;AAAA,EACA,OAAO;AACL,SAAK,OAAO,SAAS,yBAAyB,GAAG;AAC/C,aAAO,KAAK,KAAK,mBAAqB,SAAS,mBAAkB,EAAE,GAAM,SAAS,WAAW,GAAM,SAAS,2BAA2B,CAAC;AAAA,IAC1I;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,QAA0B,mBAAmB;AAAA,MAChD,OAAO;AAAA,MACP,SAAS,kBAAiB;AAAA,IAC5B,CAAC;AAAA,EACH;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,kBAAkB,CAAC;AAAA,IACzF,MAAM;AAAA,EACR,CAAC,GAAG,MAAM,CAAC;AAAA,IACT,MAAM;AAAA,IACN,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,IACR,GAAG;AAAA,MACD,MAAM;AAAA,IACR,CAAC;AAAA,EACH,GAAG;AAAA,IACD,MAAM;AAAA,EACR,GAAG;AAAA,IACD,MAAM;AAAA,IACN,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,MACN,MAAM,CAAC,2BAA2B;AAAA,IACpC,CAAC;AAAA,EACH,CAAC,GAAG,IAAI;AACV,GAAG;AACH,IAAM,sBAAN,MAAM,qBAAoB;AAAA;AAAA,EAExB,cAAc,GAAG;AACf,QAAI,KAAK,YAAY;AACnB,QAAE,eAAe;AACjB,QAAE,gBAAgB;AAAA,IACpB,OAAO;AACL,WAAK,cAAc,0BAA0B,IAAI;AACjD,UAAI,KAAK,kBAAkB;AAEzB,aAAK,iBAAiB,qBAAqB,IAAI;AAAA,MACjD,OAAO;AAEL,aAAK,cAAc,qBAAqB,IAAI;AAAA,MAC9C;AAAA,IACF;AAAA,EACF;AAAA,EACA,iBAAiB,OAAO;AACtB,SAAK,aAAa;AAClB,SAAK,UAAU,KAAK,KAAK;AAAA,EAC3B;AAAA,EACA,qBAAqB;AACnB,QAAI,CAAC,KAAK,oBAAoB,CAAC,KAAK,UAAU,CAAC,KAAK,OAAO,aAAa,CAAC,KAAK,eAAe;AAC3F;AAAA,IACF;AACA,YAAQ,QAAQ,EAAE,KAAK,MAAM;AAC3B,YAAM,iBAAiB,KAAK,eAAe;AAC3C,UAAI,KAAK,eAAe,gBAAgB;AACtC,aAAK,aAAa;AAClB,aAAK,iBAAiB,KAAK,UAAU;AACrC,aAAK,IAAI,aAAa;AAAA,MACxB;AAAA,IACF,CAAC;AAAA,EACH;AAAA,EACA,iBAAiB;AACf,UAAM,kBAAkB,KAAK,aAAa,KAAK,MAAM;AACrD,WAAO,KAAK,cAAc,gBAAgB,KAAK,UAAU,KAAK,KAAK,iBAAiB,KAAK,eAAe;AAAA,EAC1G;AAAA,EACA,aAAa,QAAQ;AACnB,WAAO,UAAQ,OAAO,SAAS,KAAK,WAAW,IAAI;AAAA,MACjD,OAAO,KAAK,qBAAqB,UAAU;AAAA,MAC3C,aAAa,KAAK,qBAAqB,UAAU;AAAA,MACjD,UAAU;AAAA,MACV,cAAc;AAAA,IAChB,CAAC;AAAA,EACH;AAAA,EACA,YAAY,eAAe,KAAK,kBAAkB,sBAAsB,gBAAgB,YAAY,QAAQ;AAC1G,SAAK,gBAAgB;AACrB,SAAK,MAAM;AACX,SAAK,mBAAmB;AACxB,SAAK,uBAAuB;AAC5B,SAAK,iBAAiB;AACtB,SAAK,aAAa;AAClB,SAAK,SAAS;AACd,SAAK,WAAW,IAAI,QAAQ;AAC5B,SAAK,QAAQ,KAAK,mBAAmB,KAAK,iBAAiB,QAAQ,IAAI;AACvE,SAAK,YAAY,IAAI,QAAQ;AAC7B,SAAK,oBAAoB;AACzB,SAAK,MAAM;AACX,SAAK,aAAa;AAClB,SAAK,aAAa;AAClB,SAAK,WAAW;AAChB,SAAK,qBAAqB;AAC1B,SAAK,gBAAgB;AACrB,QAAI,QAAQ;AACV,WAAK,OAAO,OAAO,KAAK,UAAU,KAAK,QAAQ,GAAG,OAAO,OAAK,aAAa,aAAa,CAAC,EAAE,UAAU,MAAM;AACzG,aAAK,mBAAmB;AAAA,MAC1B,CAAC;AAAA,IACH;AAAA,EACF;AAAA,EACA,WAAW;AAET,kBAAc,CAAC,KAAK,cAAc,OAAO,KAAK,cAAc,aAAa,CAAC,EAAE,KAAK,UAAU,KAAK,QAAQ,CAAC,EAAE,UAAU,CAAC,CAAC,MAAM,YAAY,MAAM;AAC7I,WAAK,oBAAoB,SAAS,WAAW,KAAK,QAAQ,eAAe;AAAA,IAC3E,CAAC;AACD,SAAK,MAAM,KAAK,eAAe;AAC/B,SAAK,eAAe,QAAQ,KAAK,UAAU,KAAK,QAAQ,CAAC,EAAE,UAAU,eAAa;AAChF,WAAK,MAAM;AAAA,IACb,CAAC;AAAA,EACH;AAAA,EACA,qBAAqB;AACnB,SAAK,iBAAiB,QAAQ,KAAK,UAAU,KAAK,QAAQ,CAAC,EAAE,UAAU,MAAM,KAAK,mBAAmB,CAAC;AACtG,SAAK,mBAAmB;AAAA,EAC1B;AAAA,EACA,YAAY,SAAS;AACnB,QAAI,QAAQ,YAAY;AACtB,WAAK,iBAAiB,KAAK,UAAU;AAAA,IACvC;AAAA,EACF;AAAA,EACA,cAAc;AACZ,SAAK,SAAS,KAAK,IAAI;AACvB,SAAK,SAAS,SAAS;AAAA,EACzB;AAAA,EACA,OAAO;AACL,SAAK,OAAO,SAAS,4BAA4B,GAAG;AAClD,aAAO,KAAK,KAAK,sBAAwB,kBAAkB,WAAW,GAAM,kBAAqB,iBAAiB,GAAM,kBAAkB,kBAAkB,CAAC,GAAM,kBAAkB,2BAA2B,GAAM,kBAAqB,gBAAgB,CAAC,GAAM,kBAAqB,YAAY,CAAC,GAAM,kBAAqB,QAAQ,CAAC,CAAC;AAAA,IAC3U;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,OAAyB,kBAAkB;AAAA,MAC9C,MAAM;AAAA,MACN,WAAW,CAAC,CAAC,IAAI,gBAAgB,EAAE,CAAC;AAAA,MACpC,gBAAgB,SAAS,mCAAmC,IAAI,KAAK,UAAU;AAC7E,YAAI,KAAK,GAAG;AACV,UAAG,eAAe,UAAU,YAAY,CAAC;AAAA,QAC3C;AACA,YAAI,KAAK,GAAG;AACV,cAAI;AACJ,UAAG,eAAe,KAAQ,YAAY,CAAC,MAAM,IAAI,mBAAmB;AAAA,QACtE;AAAA,MACF;AAAA,MACA,UAAU;AAAA,MACV,cAAc,SAAS,iCAAiC,IAAI,KAAK;AAC/D,YAAI,KAAK,GAAG;AACV,UAAG,WAAW,SAAS,SAAS,6CAA6C,QAAQ;AACnF,mBAAO,IAAI,cAAc,MAAM;AAAA,UACjC,CAAC;AAAA,QACH;AACA,YAAI,KAAK,GAAG;AACV,UAAG,YAAY,gBAAgB,IAAI,QAAQ,QAAQ,OAAO,IAAI,iBAAiB,IAAI,mBAAmB,IAAI,EAAE,iBAAiB,IAAI,QAAQ,QAAQ,IAAI,iBAAiB,IAAI,oBAAoB,MAAM,IAAI;AACxM,UAAG,YAAY,0BAA0B,IAAI,oBAAoB,EAAE,mCAAmC,IAAI,wBAAwB,IAAI,UAAU,EAAE,iCAAiC,IAAI,wBAAwB,IAAI,QAAQ,EAAE,mCAAmC,IAAI,wBAAwB,IAAI,UAAU,EAAE,iBAAiB,CAAC,IAAI,oBAAoB,EAAE,0BAA0B,CAAC,IAAI,wBAAwB,IAAI,UAAU,EAAE,wBAAwB,CAAC,IAAI,wBAAwB,IAAI,QAAQ,EAAE,0BAA0B,CAAC,IAAI,wBAAwB,IAAI,UAAU;AAAA,QACziB;AAAA,MACF;AAAA,MACA,QAAQ;AAAA,QACN,eAAe;AAAA,QACf,YAAY;AAAA,QACZ,YAAY;AAAA,QACZ,UAAU;AAAA,QACV,oBAAoB;AAAA,QACpB,eAAe;AAAA,MACjB;AAAA,MACA,UAAU,CAAC,YAAY;AAAA,MACvB,YAAY;AAAA,MACZ,UAAU,CAAI,sBAAyB,mBAAmB;AAAA,MAC1D,OAAO;AAAA,MACP,oBAAoB;AAAA,MACpB,OAAO;AAAA,MACP,MAAM;AAAA,MACN,QAAQ,CAAC,CAAC,GAAG,wBAAwB,CAAC;AAAA,MACtC,UAAU,SAAS,6BAA6B,IAAI,KAAK;AACvD,YAAI,KAAK,GAAG;AACV,UAAG,gBAAgB;AACnB,UAAG,eAAe,GAAG,QAAQ,CAAC;AAC9B,UAAG,aAAa,CAAC;AACjB,UAAG,aAAa;AAAA,QAClB;AAAA,MACF;AAAA,MACA,eAAe;AAAA,MACf,iBAAiB;AAAA,IACnB,CAAC;AAAA,EACH;AACF;AACA,WAAW,CAAC,aAAa,CAAC,GAAG,oBAAoB,WAAW,cAAc,MAAM;AAChF,WAAW,CAAC,aAAa,CAAC,GAAG,oBAAoB,WAAW,cAAc,MAAM;AAChF,WAAW,CAAC,aAAa,CAAC,GAAG,oBAAoB,WAAW,YAAY,MAAM;AAC9E,WAAW,CAAC,aAAa,CAAC,GAAG,oBAAoB,WAAW,sBAAsB,MAAM;AACxF,WAAW,CAAC,aAAa,CAAC,GAAG,oBAAoB,WAAW,iBAAiB,MAAM;AAAA,CAClF,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,qBAAqB,CAAC;AAAA,IAC5F,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,UAAU;AAAA,MACV,iBAAiB,wBAAwB;AAAA,MACzC,eAAe,oBAAkB;AAAA,MACjC,qBAAqB;AAAA,MACrB,UAAU;AAAA;AAAA;AAAA;AAAA;AAAA,MAKV,MAAM;AAAA,QACJ,kCAAkC;AAAA,QAClC,2CAA2C;AAAA,QAC3C,yCAAyC;AAAA,QACzC,2CAA2C;AAAA,QAC3C,yBAAyB;AAAA,QACzB,kCAAkC;AAAA,QAClC,gCAAgC;AAAA,QAChC,kCAAkC;AAAA,QAClC,0BAA0B;AAAA,QAC1B,2BAA2B;AAAA,QAC3B,WAAW;AAAA,MACb;AAAA,MACA,YAAY;AAAA,IACd,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC;AAAA,IACT,MAAM;AAAA,EACR,GAAG;AAAA,IACD,MAAS;AAAA,EACX,GAAG;AAAA,IACD,MAAM;AAAA,IACN,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,IACR,CAAC;AAAA,EACH,GAAG;AAAA,IACD,MAAM;AAAA,IACN,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,MACN,MAAM,CAAC,2BAA2B;AAAA,IACpC,CAAC;AAAA,EACH,GAAG;AAAA,IACD,MAAS;AAAA,IACT,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,IACR,CAAC;AAAA,EACH,GAAG;AAAA,IACD,MAAS;AAAA,IACT,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,IACR,CAAC;AAAA,EACH,GAAG;AAAA,IACD,MAAS;AAAA,IACT,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,IACR,CAAC;AAAA,EACH,CAAC,GAAG;AAAA,IACF,eAAe,CAAC;AAAA,MACd,MAAM;AAAA,IACR,CAAC;AAAA,IACD,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,IACR,CAAC;AAAA,IACD,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,IACR,CAAC;AAAA,IACD,UAAU,CAAC;AAAA,MACT,MAAM;AAAA,IACR,CAAC;AAAA,IACD,oBAAoB,CAAC;AAAA,MACnB,MAAM;AAAA,IACR,CAAC;AAAA,IACD,eAAe,CAAC;AAAA,MACd,MAAM;AAAA,IACR,CAAC;AAAA,IACD,kBAAkB,CAAC;AAAA,MACjB,MAAM;AAAA,MACN,MAAM,CAAC,YAAY;AAAA,QACjB,aAAa;AAAA,MACf,CAAC;AAAA,IACH,CAAC;AAAA,EACH,CAAC;AACH,GAAG;AACH,IAAM,gCAAN,MAAM,+BAA8B;AAAA,EAClC,YAAY,YAAY,UAAU,gBAAgB;AAChD,SAAK,aAAa;AAClB,SAAK,WAAW;AAChB,SAAK,iBAAiB;AACtB,SAAK,iBAAiB;AACtB,SAAK,YAAY;AACjB,SAAK,OAAO;AACZ,SAAK,SAAS;AACd,SAAK,uBAAuB,CAAC;AAC7B,SAAK,cAAc;AACnB,SAAK,MAAM;AACX,SAAK,WAAW,IAAI,QAAQ;AAAA,EAC9B;AAAA,EACA,kBAAkB;AAChB,QAAI,KAAK,QAAQ;AACf,WAAK,cAAc;AAAA,IACrB,OAAO;AACL,WAAK,cAAc;AAAA,IACrB;AAAA,EACF;AAAA,EACA,WAAW;AACT,SAAK,gBAAgB;AACrB,SAAK,MAAM,KAAK,eAAe;AAC/B,SAAK,eAAe,QAAQ,KAAK,UAAU,KAAK,QAAQ,CAAC,EAAE,UAAU,eAAa;AAChF,WAAK,MAAM;AAAA,IACb,CAAC;AAAA,EACH;AAAA,EACA,YAAY,SAAS;AACnB,UAAM;AAAA,MACJ;AAAA,MACA;AAAA,MACA;AAAA,IACF,IAAI;AACJ,QAAI,QAAQ,QAAQ;AAClB,WAAK,gBAAgB;AAAA,IACvB;AACA,QAAI,WAAW;AACb,UAAI,KAAK,qBAAqB,QAAQ;AACpC,aAAK,qBAAqB,OAAO,UAAQ,CAAC,CAAC,IAAI,EAAE,QAAQ,eAAa;AACpE,eAAK,SAAS,YAAY,KAAK,WAAW,eAAe,SAAS;AAAA,QACpE,CAAC;AAAA,MACH;AACA,UAAI,KAAK,WAAW;AAClB,aAAK,uBAAuB,KAAK,UAAU,MAAM,GAAG;AACpD,aAAK,qBAAqB,OAAO,UAAQ,CAAC,CAAC,IAAI,EAAE,QAAQ,eAAa;AACpE,eAAK,SAAS,SAAS,KAAK,WAAW,eAAe,SAAS;AAAA,QACjE,CAAC;AAAA,MACH;AAAA,IACF;AAAA,EACF;AAAA,EACA,cAAc;AACZ,SAAK,SAAS,KAAK;AACnB,SAAK,SAAS,SAAS;AAAA,EACzB;AAAA,EACA,OAAO;AACL,SAAK,OAAO,SAAS,sCAAsC,GAAG;AAC5D,aAAO,KAAK,KAAK,gCAAkC,kBAAqB,UAAU,GAAM,kBAAqB,SAAS,GAAM,kBAAqB,gBAAgB,CAAC,CAAC;AAAA,IACrK;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,OAAyB,kBAAkB;AAAA,MAC9C,MAAM;AAAA,MACN,WAAW,CAAC,CAAC,IAAI,2BAA2B,EAAE,CAAC;AAAA,MAC/C,WAAW,CAAC,GAAG,YAAY,mBAAmB,cAAc;AAAA,MAC5D,UAAU;AAAA,MACV,cAAc,SAAS,2CAA2C,IAAI,KAAK;AACzE,YAAI,KAAK,GAAG;AACV,UAAG,wBAAwB,mBAAmB,IAAI,WAAW;AAC7D,UAAG,YAAY,gBAAgB,IAAI,QAAQ,KAAK;AAAA,QAClD;AAAA,MACF;AAAA,MACA,QAAQ;AAAA,QACN,gBAAgB;AAAA,QAChB,WAAW;AAAA,QACX,MAAM;AAAA,QACN,QAAQ;AAAA,MACV;AAAA,MACA,UAAU,CAAC,sBAAsB;AAAA,MACjC,YAAY;AAAA,MACZ,UAAU,CAAI,sBAAyB,mBAAmB;AAAA,MAC1D,OAAO;AAAA,MACP,OAAO;AAAA,MACP,MAAM;AAAA,MACN,QAAQ,CAAC,CAAC,GAAG,kBAAkB,CAAC;AAAA,MAChC,UAAU,SAAS,uCAAuC,IAAI,KAAK;AACjE,YAAI,KAAK,GAAG;AACV,UAAG,WAAW,GAAG,sDAAsD,GAAG,GAAG,eAAe,CAAC;AAAA,QAC/F;AACA,YAAI,KAAK,GAAG;AACV,UAAG,WAAW,oBAAoB,IAAI,cAAc;AAAA,QACtD;AAAA,MACF;AAAA,MACA,cAAc,CAAC,gBAAgB;AAAA,MAC/B,eAAe;AAAA,MACf,MAAM;AAAA,QACJ,WAAW,CAAC,cAAc;AAAA,MAC5B;AAAA,MACA,iBAAiB;AAAA,IACnB,CAAC;AAAA,EACH;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,+BAA+B,CAAC;AAAA,IACtG,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,YAAY,CAAC,cAAc;AAAA,MAC3B,UAAU;AAAA,MACV,eAAe,oBAAkB;AAAA,MACjC,iBAAiB,wBAAwB;AAAA,MACzC,UAAU;AAAA,MACV,MAAM;AAAA,QACJ,OAAO;AAAA,QACP,wBAAwB;AAAA,QACxB,qBAAqB;AAAA,MACvB;AAAA,MACA,SAAS,CAAC,gBAAgB;AAAA,MAC1B,YAAY;AAAA,IACd,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC;AAAA,IACT,MAAS;AAAA,EACX,GAAG;AAAA,IACD,MAAS;AAAA,EACX,GAAG;AAAA,IACD,MAAS;AAAA,IACT,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,IACR,CAAC;AAAA,EACH,CAAC,GAAG;AAAA,IACF,gBAAgB,CAAC;AAAA,MACf,MAAM;AAAA,IACR,CAAC;AAAA,IACD,WAAW,CAAC;AAAA,MACV,MAAM;AAAA,IACR,CAAC;AAAA,IACD,MAAM,CAAC;AAAA,MACL,MAAM;AAAA,IACR,CAAC;AAAA,IACD,QAAQ,CAAC;AAAA,MACP,MAAM;AAAA,IACR,CAAC;AAAA,EACH,CAAC;AACH,GAAG;AACH,IAAM,oCAAN,MAAM,mCAAkC;AAAA,EACtC,YAAY,gBAAgB;AAC1B,SAAK,iBAAiB;AACtB,SAAK,YAAY;AACjB,SAAK,QAAQ;AACb,SAAK,iBAAiB;AACtB,SAAK,uBAAuB;AAC5B,SAAK,OAAO;AACZ,SAAK,WAAW;AAChB,SAAK,aAAa;AAClB,SAAK,SAAS;AACd,SAAK,oBAAoB,IAAI,aAAa;AAC1C,SAAK,cAAc;AACnB,SAAK,MAAM;AACX,SAAK,WAAW,IAAI,QAAQ;AAAA,EAC9B;AAAA,EACA,cAAc,OAAO;AACnB,QAAI,CAAC,KAAK,YAAY;AACpB,WAAK,kBAAkB,KAAK,KAAK;AAAA,IACnC;AAAA,EACF;AAAA,EACA,cAAc;AACZ,SAAK,SAAS,KAAK;AACnB,SAAK,SAAS,SAAS;AAAA,EACzB;AAAA,EACA,kBAAkB;AAChB,QAAI,KAAK,QAAQ;AACf,UAAI,KAAK,SAAS,cAAc;AAC9B,aAAK,cAAc;AAAA,MACrB,WAAW,KAAK,SAAS,YAAY;AACnC,aAAK,cAAc;AAAA,MACrB;AAAA,IACF,OAAO;AACL,WAAK,cAAc;AAAA,IACrB;AAAA,EACF;AAAA,EACA,WAAW;AACT,SAAK,gBAAgB;AACrB,SAAK,MAAM,KAAK,eAAe;AAC/B,SAAK,eAAe,QAAQ,KAAK,UAAU,KAAK,QAAQ,CAAC,EAAE,UAAU,eAAa;AAChF,WAAK,MAAM;AAAA,IACb,CAAC;AAAA,EACH;AAAA,EACA,YAAY,SAAS;AACnB,UAAM;AAAA,MACJ;AAAA,MACA;AAAA,IACF,IAAI;AACJ,QAAI,QAAQ,QAAQ;AAClB,WAAK,gBAAgB;AAAA,IACvB;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,OAAO,SAAS,0CAA0C,GAAG;AAChE,aAAO,KAAK,KAAK,oCAAsC,kBAAqB,gBAAgB,CAAC,CAAC;AAAA,IAChG;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,OAAyB,kBAAkB;AAAA,MAC9C,MAAM;AAAA,MACN,WAAW,CAAC,CAAC,IAAI,gCAAgC,EAAE,CAAC;AAAA,MACpD,WAAW,CAAC,GAAG,oBAAoB,wBAAwB;AAAA,MAC3D,UAAU;AAAA,MACV,cAAc,SAAS,+CAA+C,IAAI,KAAK;AAC7E,YAAI,KAAK,GAAG;AACV,UAAG,WAAW,cAAc,SAAS,kEAAkE;AACrG,mBAAO,IAAI,cAAc,IAAI;AAAA,UAC/B,CAAC,EAAE,cAAc,SAAS,kEAAkE;AAC1F,mBAAO,IAAI,cAAc,KAAK;AAAA,UAChC,CAAC;AAAA,QACH;AACA,YAAI,KAAK,GAAG;AACV,UAAG,wBAAwB,gBAAgB,IAAI,WAAW,EAAE,kBAAkB,IAAI,WAAW;AAC7F,UAAG,YAAY,kBAAkB,IAAI,UAAU,OAAO,EAAE,iBAAiB,IAAI,UAAU,MAAM,EAAE,qCAAqC,IAAI,SAAS,YAAY,EAAE,oCAAoC,IAAI,SAAS,cAAc,IAAI,aAAa,OAAO,EAAE,mCAAmC,IAAI,SAAS,cAAc,IAAI,aAAa,MAAM,EAAE,wBAAwB,IAAI,QAAQ,KAAK;AAAA,QAC1X;AAAA,MACF;AAAA,MACA,QAAQ;AAAA,QACN,WAAW;AAAA,QACX,OAAO;AAAA,QACP,gBAAgB;AAAA,QAChB,sBAAsB;AAAA,QACtB,MAAM;AAAA,QACN,UAAU;AAAA,QACV,YAAY;AAAA,QACZ,QAAQ;AAAA,MACV;AAAA,MACA,SAAS;AAAA,QACP,mBAAmB;AAAA,MACrB;AAAA,MACA,UAAU,CAAC,0BAA0B;AAAA,MACrC,YAAY;AAAA,MACZ,UAAU,CAAI,sBAAyB,mBAAmB;AAAA,MAC1D,OAAO;AAAA,MACP,OAAO;AAAA,MACP,MAAM;AAAA,MACN,QAAQ,CAAC,CAAC,GAAG,SAAS,GAAG,CAAC,GAAG,kBAAkB,CAAC;AAAA,MAChD,UAAU,SAAS,2CAA2C,IAAI,KAAK;AACrE,YAAI,KAAK,GAAG;AACV,UAAG,eAAe,GAAG,OAAO,CAAC;AAC7B,UAAG,WAAW,GAAG,0DAA0D,GAAG,GAAG,eAAe,CAAC;AACjG,UAAG,aAAa;AAAA,QAClB;AACA,YAAI,KAAK,GAAG;AACV,UAAG,YAAY,qBAAqB,IAAI,oBAAoB,EAAE,YAAY,CAAC,IAAI,oBAAoB,EAAE,8BAA8B,IAAI,oBAAoB,EAAE,qBAAqB,CAAC,IAAI,oBAAoB,EAAE,yBAAyB,IAAI,oBAAoB,EAAE,gBAAgB,CAAC,IAAI,oBAAoB,EAAE,gBAAgB,IAAI,QAAQ,KAAK;AAC5U,UAAG,WAAW,WAAW,IAAI,SAAS;AACtC,UAAG,UAAU;AACb,UAAG,WAAW,oBAAoB,IAAI,cAAc;AAAA,QACtD;AAAA,MACF;AAAA,MACA,cAAc,CAAC,SAAS,gBAAgB;AAAA,MACxC,eAAe;AAAA,MACf,MAAM;AAAA,QACJ,WAAW,CAAC,eAAe,WAAW;AAAA,MACxC;AAAA,MACA,iBAAiB;AAAA,IACnB,CAAC;AAAA,EACH;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,mCAAmC,CAAC;AAAA,IAC1G,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,UAAU;AAAA,MACV,eAAe,oBAAkB;AAAA,MACjC,YAAY,CAAC,eAAe,WAAW;AAAA,MACvC,iBAAiB,wBAAwB;AAAA,MACzC,UAAU;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MAcV,MAAM;AAAA,QACJ,OAAO;AAAA,QACP,0BAA0B;AAAA,QAC1B,yBAAyB;AAAA,QACzB,6CAA6C;AAAA,QAC7C,4CAA4C;AAAA,QAC5C,2CAA2C;AAAA,QAC3C,gCAAgC;AAAA,QAChC,kBAAkB;AAAA,QAClB,oBAAoB;AAAA,QACpB,gBAAgB;AAAA,QAChB,gBAAgB;AAAA,MAClB;AAAA,MACA,SAAS,CAAC,SAAS,gBAAgB;AAAA,MACnC,YAAY;AAAA,IACd,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC;AAAA,IACT,MAAS;AAAA,IACT,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,IACR,CAAC;AAAA,EACH,CAAC,GAAG;AAAA,IACF,WAAW,CAAC;AAAA,MACV,MAAM;AAAA,IACR,CAAC;AAAA,IACD,OAAO,CAAC;AAAA,MACN,MAAM;AAAA,IACR,CAAC;AAAA,IACD,gBAAgB,CAAC;AAAA,MACf,MAAM;AAAA,IACR,CAAC;AAAA,IACD,sBAAsB,CAAC;AAAA,MACrB,MAAM;AAAA,IACR,CAAC;AAAA,IACD,MAAM,CAAC;AAAA,MACL,MAAM;AAAA,IACR,CAAC;AAAA,IACD,UAAU,CAAC;AAAA,MACT,MAAM;AAAA,IACR,CAAC;AAAA,IACD,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,IACR,CAAC;AAAA,IACD,QAAQ,CAAC;AAAA,MACP,MAAM;AAAA,IACR,CAAC;AAAA,IACD,mBAAmB,CAAC;AAAA,MAClB,MAAM;AAAA,IACR,CAAC;AAAA,EACH,CAAC;AACH,GAAG;AACH,IAAM,0BAAN,MAAM,yBAAwB;AAAA,EAC5B,YAAY,KAAK,gBAAgB;AAC/B,SAAK,MAAM;AACX,SAAK,iBAAiB;AACtB,SAAK,SAAS;AACd,SAAK,UAAU;AACf,SAAK,uBAAuB;AAC5B,SAAK,aAAa;AAClB,SAAK,cAAc;AACnB,SAAK,OAAO;AACZ,SAAK,gBAAgB,IAAI,aAAa;AACtC,SAAK,oBAAoB,IAAI,aAAa;AAC1C,SAAK,MAAM;AACX,SAAK,WAAW,IAAI,QAAQ;AAAA,EAC9B;AAAA,EACA,WAAW;AACT,SAAK,MAAM,KAAK,eAAe;AAC/B,SAAK,eAAe,QAAQ,KAAK,UAAU,KAAK,QAAQ,CAAC,EAAE,UAAU,eAAa;AAChF,WAAK,MAAM;AACX,WAAK,IAAI,cAAc;AAAA,IACzB,CAAC;AAAA,EACH;AAAA,EACA,cAAc;AACZ,SAAK,SAAS,KAAK;AACnB,SAAK,SAAS,SAAS;AAAA,EACzB;AAAA,EACA,cAAc,OAAO;AACnB,QAAI,CAAC,KAAK,YAAY;AACpB,WAAK,kBAAkB,KAAK,KAAK;AAAA,IACnC;AAAA,EACF;AAAA,EACA,aAAa;AACX,QAAI,KAAK,SAAS,YAAY,CAAC,KAAK,YAAY;AAC9C,WAAK,cAAc,KAAK;AAAA,IAC1B;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,OAAO,SAAS,gCAAgC,GAAG;AACtD,aAAO,KAAK,KAAK,0BAA4B,kBAAqB,iBAAiB,GAAM,kBAAqB,gBAAgB,CAAC,CAAC;AAAA,IAClI;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,OAAyB,kBAAkB;AAAA,MAC9C,MAAM;AAAA,MACN,WAAW,CAAC,CAAC,IAAI,oBAAoB,EAAE,CAAC;AAAA,MACxC,UAAU;AAAA,MACV,cAAc,SAAS,qCAAqC,IAAI,KAAK;AACnE,YAAI,KAAK,GAAG;AACV,UAAG,WAAW,SAAS,SAAS,mDAAmD;AACjF,mBAAO,IAAI,WAAW;AAAA,UACxB,CAAC,EAAE,cAAc,SAAS,wDAAwD;AAChF,mBAAO,IAAI,cAAc,IAAI;AAAA,UAC/B,CAAC,EAAE,cAAc,SAAS,wDAAwD;AAChF,mBAAO,IAAI,cAAc,KAAK;AAAA,UAChC,CAAC;AAAA,QACH;AACA,YAAI,KAAK,GAAG;AACV,UAAG,YAAY,gBAAgB,IAAI,QAAQ,QAAQ,OAAO,IAAI,aAAa,IAAI,EAAE,iBAAiB,IAAI,QAAQ,QAAQ,IAAI,cAAc,MAAM,IAAI;AAClJ,UAAG,YAAY,mCAAmC,IAAI,oBAAoB,EAAE,0BAA0B,CAAC,IAAI,oBAAoB;AAAA,QACjI;AAAA,MACF;AAAA,MACA,QAAQ;AAAA,QACN,QAAQ;AAAA,QACR,SAAS;AAAA,QACT,sBAAsB;AAAA,QACtB,YAAY;AAAA,QACZ,aAAa;AAAA,QACb,MAAM;AAAA,MACR;AAAA,MACA,SAAS;AAAA,QACP,eAAe;AAAA,QACf,mBAAmB;AAAA,MACrB;AAAA,MACA,UAAU,CAAC,gBAAgB;AAAA,MAC3B,YAAY;AAAA,MACZ,UAAU,CAAI,mBAAmB;AAAA,MACjC,OAAO;AAAA,MACP,oBAAoB;AAAA,MACpB,OAAO;AAAA,MACP,MAAM;AAAA,MACN,QAAQ,CAAC,CAAC,WAAW,IAAI,GAAG,QAAQ,GAAG,CAAC,GAAG,wBAAwB,GAAG,CAAC,GAAG,uCAAuC,GAAG,CAAC,GAAG,wBAAwB,GAAG,CAAC,WAAW,IAAI,UAAU,QAAQ,GAAG,sCAAsC,GAAG,CAAC,WAAW,IAAI,UAAU,SAAS,GAAG,sCAAsC,GAAG,CAAC,GAAG,wBAAwB,CAAC;AAAA,MAC7U,UAAU,SAAS,iCAAiC,IAAI,KAAK;AAC3D,YAAI,KAAK,GAAG;AACV,UAAG,gBAAgB;AACnB,UAAG,WAAW,GAAG,gDAAgD,GAAG,GAAG,QAAQ,CAAC,EAAE,GAAG,iDAAiD,GAAG,GAAG,gBAAgB,CAAC;AAC7J,UAAG,aAAa,CAAC;AACjB,UAAG,WAAW,GAAG,gDAAgD,GAAG,GAAG,QAAQ,CAAC,EAAE,GAAG,gDAAgD,GAAG,CAAC;AAAA,QAC3I;AACA,YAAI,KAAK,GAAG;AACV,UAAG,cAAc,GAAG,IAAI,SAAS,IAAI,EAAE;AACvC,UAAG,UAAU;AACb,UAAG,WAAW,0BAA0B,IAAI,OAAO;AACnD,UAAG,UAAU,CAAC;AACd,UAAG,cAAc,GAAG,IAAI,uBAAuB,IAAI,CAAC;AAAA,QACtD;AAAA,MACF;AAAA,MACA,cAAc,CAAC,cAAiB,iBAAiB,gBAAmB,+BAA+B;AAAA,MACnG,eAAe;AAAA,MACf,iBAAiB;AAAA,IACnB,CAAC;AAAA,EACH;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,yBAAyB,CAAC;AAAA,IAChG,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,UAAU;AAAA,MACV,eAAe,oBAAkB;AAAA,MACjC,iBAAiB,wBAAwB;AAAA,MACzC,UAAU;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MAuBV,MAAM;AAAA,QACJ,2CAA2C;AAAA,QAC3C,kCAAkC;AAAA,QAClC,0BAA0B;AAAA,QAC1B,2BAA2B;AAAA,QAC3B,WAAW;AAAA,QACX,gBAAgB;AAAA,QAChB,gBAAgB;AAAA,MAClB;AAAA,MACA,SAAS,CAAC,cAAc,cAAc;AAAA,MACtC,YAAY;AAAA,IACd,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC;AAAA,IACT,MAAS;AAAA,EACX,GAAG;AAAA,IACD,MAAS;AAAA,IACT,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,IACR,CAAC;AAAA,EACH,CAAC,GAAG;AAAA,IACF,QAAQ,CAAC;AAAA,MACP,MAAM;AAAA,IACR,CAAC;AAAA,IACD,SAAS,CAAC;AAAA,MACR,MAAM;AAAA,IACR,CAAC;AAAA,IACD,sBAAsB,CAAC;AAAA,MACrB,MAAM;AAAA,IACR,CAAC;AAAA,IACD,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,IACR,CAAC;AAAA,IACD,aAAa,CAAC;AAAA,MACZ,MAAM;AAAA,IACR,CAAC;AAAA,IACD,MAAM,CAAC;AAAA,MACL,MAAM;AAAA,IACR,CAAC;AAAA,IACD,eAAe,CAAC;AAAA,MACd,MAAM;AAAA,IACR,CAAC;AAAA,IACD,mBAAmB,CAAC;AAAA,MAClB,MAAM;AAAA,IACR,CAAC;AAAA,EACH,CAAC;AACH,GAAG;AACH,IAAM,0BAA0B,CAAC,aAAa,UAAU,aAAa,OAAO,aAAa,aAAa,aAAa,SAAS,aAAa,MAAM,aAAa,UAAU;AACtK,IAAM,4BAA4B,CAAC,aAAa,YAAY,aAAa,aAAa,aAAa,UAAU,aAAa,OAAO;AACjI,IAAM,qBAAN,MAAM,oBAAmB;AAAA;AAAA,EAEvB,4BAA4B,MAAM;AAChC,SAAK,iBAAiB,4BAA4B,IAAI;AAAA,EACxD;AAAA,EACA,gBAAgB;AACd,SAAK,4BAA4B,CAAC,KAAK,MAAM;AAAA,EAC/C;AAAA,EACA,mBAAmB,OAAO;AACxB,SAAK,WAAW;AAChB,QAAI,KAAK,SAAS,UAAU;AAC1B,WAAK,iBAAiB,iCAAiC,KAAK;AAAA,IAC9D;AAAA,EACF;AAAA,EACA,kBAAkB;AAChB,QAAI,KAAK,SAAS,gBAAgB,KAAK,SAAS,aAAa,KAAK,oBAAoB,KAAK,gBAAgB,cAAc;AAEvH,WAAK,eAAe,KAAK,iBAAiB,cAAc,sBAAsB,EAAE;AAAA,IAClF;AAAA,EACF;AAAA,EACA,iBAAiB,UAAU;AACzB,UAAM,YAAY,iBAAiB,QAAQ;AAC3C,QAAI,cAAc,cAAc,cAAc,iBAAiB,cAAc,SAAS;AACpF,WAAK,WAAW;AAAA,IAClB,WAAW,cAAc,aAAa,cAAc,gBAAgB,cAAc,QAAQ;AACxF,WAAK,WAAW;AAAA,IAClB;AAAA,EACF;AAAA,EACA,YAAY,eAAe,KAAK,kBAAkB,UAAU,sBAAsB,gBAAgB,aAAa;AAC7G,SAAK,gBAAgB;AACrB,SAAK,MAAM;AACX,SAAK,mBAAmB;AACxB,SAAK,WAAW;AAChB,SAAK,uBAAuB;AAC5B,SAAK,iBAAiB;AACtB,SAAK,cAAc;AACnB,SAAK,kBAAkB;AACvB,SAAK,gBAAgB;AACrB,SAAK,UAAU;AACf,SAAK,SAAS;AACd,SAAK,SAAS;AACd,SAAK,aAAa;AAClB,SAAK,cAAc;AACnB,SAAK,eAAe,IAAI,aAAa;AACrC,SAAK,mBAAmB;AAGxB,SAAK,2BAA2B;AAChC,SAAK,4BAA4B;AACjC,SAAK,QAAQ,KAAK,iBAAiB;AACnC,SAAK,WAAW,IAAI,QAAQ;AAC5B,SAAK,WAAW;AAChB,SAAK,eAAe;AACpB,SAAK,QAAQ;AACb,SAAK,OAAO;AACZ,SAAK,oBAAoB;AACzB,SAAK,mBAAmB;AACxB,SAAK,aAAa;AAClB,SAAK,WAAW;AAChB,SAAK,MAAM;AAAA,EACb;AAAA,EACA,WAAW;AAET,SAAK,cAAc,OAAO,KAAK,UAAU,KAAK,QAAQ,CAAC,EAAE,UAAU,WAAS;AAC1E,WAAK,QAAQ;AACb,WAAK,IAAI,aAAa;AAAA,IACxB,CAAC;AAED,SAAK,iBAAiB,MAAM,KAAK,UAAU,KAAK,QAAQ,CAAC,EAAE,UAAU,UAAQ;AAC3E,WAAK,OAAO;AACZ,UAAI,SAAS,cAAc;AACzB,aAAK,mBAAmB,CAAC,aAAa,KAAK,WAAW,GAAG,GAAG,yBAAyB;AAAA,MACvF,WAAW,SAAS,YAAY;AAC9B,aAAK,mBAAmB;AAAA,MAC1B;AACA,WAAK,IAAI,aAAa;AAAA,IACxB,CAAC;AAED,kBAAc,CAAC,KAAK,iBAAiB,OAAO,KAAK,cAAc,aAAa,CAAC,EAAE,KAAK,UAAU,KAAK,QAAQ,CAAC,EAAE,UAAU,CAAC,CAAC,MAAM,YAAY,MAAM;AAChJ,WAAK,oBAAoB,SAAS,WAAW,KAAK,QAAQ,eAAe;AACzE,WAAK,IAAI,aAAa;AAAA,IACxB,CAAC;AAED,SAAK,iBAAiB,sBAAsB,KAAK,UAAU,KAAK,QAAQ,CAAC,EAAE,UAAU,UAAQ;AAC3F,WAAK,WAAW;AAChB,UAAI,SAAS,KAAK,QAAQ;AACxB,aAAK,gBAAgB;AACrB,aAAK,SAAS;AACd,aAAK,aAAa,KAAK,KAAK,MAAM;AAClC,aAAK,IAAI,aAAa;AAAA,MACxB;AAAA,IACF,CAAC;AACD,SAAK,MAAM,KAAK,eAAe;AAC/B,SAAK,eAAe,QAAQ,KAAK,UAAU,KAAK,QAAQ,CAAC,EAAE,UAAU,eAAa;AAChF,WAAK,MAAM;AACX,WAAK,IAAI,aAAa;AAAA,IACxB,CAAC;AAAA,EACH;AAAA,EACA,qBAAqB;AACnB,SAAK,gBAAgB;AACrB,UAAM,4BAA4B,KAAK;AACvC,UAAM,UAAU,0BAA0B;AAC1C,UAAM,mBAAmB,MAAM,GAAG,CAAC,SAAS,GAAG,0BAA0B,IAAI,UAAQ,KAAK,SAAS,CAAC,CAAC;AACrG,YAAQ,KAAK,UAAU,yBAAyB,GAAG,UAAU,MAAM,gBAAgB,GAAG,UAAU,IAAI,GAAG,IAAI,MAAM,0BAA0B,KAAK,OAAK,EAAE,UAAU,CAAC,GAAG,UAAU,KAAK,QAAQ,CAAC,EAAE,UAAU,cAAY;AACnN,WAAK,aAAa;AAClB,WAAK,IAAI,aAAa;AAAA,IACxB,CAAC;AAAA,EACH;AAAA,EACA,YAAY,SAAS;AACnB,UAAM;AAAA,MACJ;AAAA,IACF,IAAI;AACJ,QAAI,QAAQ;AACV,WAAK,iBAAiB,4BAA4B,KAAK,MAAM;AAC7D,WAAK,gBAAgB;AAAA,IACvB;AAAA,EACF;AAAA,EACA,cAAc;AACZ,SAAK,SAAS,KAAK;AACnB,SAAK,SAAS,SAAS;AAAA,EACzB;AAAA,EACA,OAAO;AACL,SAAK,OAAO,SAAS,2BAA2B,GAAG;AACjD,aAAO,KAAK,KAAK,qBAAuB,kBAAkB,WAAW,GAAM,kBAAqB,iBAAiB,GAAM,kBAAkB,gBAAgB,GAAM,kBAAuB,QAAQ,GAAM,kBAAkB,2BAA2B,GAAM,kBAAqB,gBAAgB,CAAC,GAAM,kBAAqB,wBAAwB,CAAC,CAAC;AAAA,IACpV;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,OAAyB,kBAAkB;AAAA,MAC9C,MAAM;AAAA,MACN,WAAW,CAAC,CAAC,IAAI,cAAc,EAAE,CAAC;AAAA,MAClC,gBAAgB,SAAS,kCAAkC,IAAI,KAAK,UAAU;AAC5E,YAAI,KAAK,GAAG;AACV,UAAG,eAAe,UAAU,qBAAoB,CAAC;AACjD,UAAG,eAAe,UAAU,qBAAqB,CAAC;AAAA,QACpD;AACA,YAAI,KAAK,GAAG;AACV,cAAI;AACJ,UAAG,eAAe,KAAQ,YAAY,CAAC,MAAM,IAAI,2BAA2B;AAC5E,UAAG,eAAe,KAAQ,YAAY,CAAC,MAAM,IAAI,4BAA4B;AAAA,QAC/E;AAAA,MACF;AAAA,MACA,WAAW,SAAS,yBAAyB,IAAI,KAAK;AACpD,YAAI,KAAK,GAAG;AACV,UAAG,YAAY,kBAAkB,GAAG,UAAU;AAAA,QAChD;AACA,YAAI,KAAK,GAAG;AACV,cAAI;AACJ,UAAG,eAAe,KAAQ,YAAY,CAAC,MAAM,IAAI,mBAAmB,GAAG;AAAA,QACzE;AAAA,MACF;AAAA,MACA,UAAU;AAAA,MACV,cAAc,SAAS,gCAAgC,IAAI,KAAK;AAC9D,YAAI,KAAK,GAAG;AACV,UAAG,YAAY,6BAA6B,IAAI,oBAAoB,EAAE,sCAAsC,IAAI,wBAAwB,IAAI,UAAU,EAAE,kCAAkC,IAAI,wBAAwB,IAAI,MAAM,EAAE,sCAAsC,IAAI,wBAAwB,IAAI,UAAU,EAAE,sCAAsC,IAAI,wBAAwB,IAAI,SAAS,UAAU,EAAE,wCAAwC,IAAI,wBAAwB,IAAI,SAAS,YAAY,EAAE,oCAAoC,IAAI,wBAAwB,IAAI,SAAS,QAAQ,EAAE,oCAAoC,IAAI,wBAAwB,IAAI,QAAQ,EAAE,oBAAoB,CAAC,IAAI,oBAAoB,EAAE,6BAA6B,CAAC,IAAI,wBAAwB,IAAI,UAAU,EAAE,yBAAyB,CAAC,IAAI,wBAAwB,IAAI,MAAM,EAAE,6BAA6B,CAAC,IAAI,wBAAwB,IAAI,UAAU,EAAE,6BAA6B,CAAC,IAAI,wBAAwB,IAAI,SAAS,UAAU,EAAE,+BAA+B,CAAC,IAAI,wBAAwB,IAAI,SAAS,YAAY,EAAE,2BAA2B,CAAC,IAAI,wBAAwB,IAAI,SAAS,QAAQ,EAAE,2BAA2B,CAAC,IAAI,wBAAwB,IAAI,QAAQ,EAAE,wBAAwB,IAAI,QAAQ,KAAK;AAAA,QAClwC;AAAA,MACF;AAAA,MACA,QAAQ;AAAA,QACN,iBAAiB;AAAA,QACjB,eAAe;AAAA,QACf,SAAS;AAAA,QACT,QAAQ;AAAA,QACR,QAAQ;AAAA,QACR,YAAY;AAAA,QACZ,aAAa;AAAA,MACf;AAAA,MACA,SAAS;AAAA,QACP,cAAc;AAAA,MAChB;AAAA,MACA,UAAU,CAAC,WAAW;AAAA,MACtB,YAAY;AAAA,MACZ,UAAU,CAAI,mBAAmB,CAAC,gBAAgB,CAAC,GAAM,sBAAyB,mBAAmB;AAAA,MACrG,OAAO;AAAA,MACP,oBAAoB;AAAA,MACpB,OAAO;AAAA,MACP,MAAM;AAAA,MACN,QAAQ,CAAC,CAAC,UAAU,kBAAkB,GAAG,CAAC,mBAAmB,EAAE,GAAG,CAAC,oBAAoB,IAAI,oBAAoB,IAAI,GAAG,qBAAqB,iBAAiB,UAAU,WAAW,QAAQ,cAAc,wBAAwB,aAAa,GAAG,CAAC,2BAA2B,IAAI,GAAG,QAAQ,UAAU,iBAAiB,aAAa,gBAAgB,GAAG,CAAC,uBAAuB,IAAI,GAAG,kBAAkB,gCAAgC,6BAA6B,4BAA4B,2BAA2B,sCAAsC,GAAG,CAAC,gCAAgC,IAAI,GAAG,qBAAqB,SAAS,QAAQ,UAAU,YAAY,cAAc,wBAAwB,kBAAkB,aAAa,eAAe,CAAC;AAAA,MAC5tB,UAAU,SAAS,4BAA4B,IAAI,KAAK;AACtD,YAAI,KAAK,GAAG;AACV,gBAAM,MAAS,iBAAiB;AAChC,UAAG,gBAAgB,GAAG;AACtB,UAAG,eAAe,GAAG,OAAO,GAAG,CAAC;AAChC,UAAG,WAAW,qBAAqB,SAAS,6DAA6D,QAAQ;AAC/G,YAAG,cAAc,GAAG;AACpB,mBAAU,YAAY,IAAI,mBAAmB,MAAM,CAAC;AAAA,UACtD,CAAC,EAAE,iBAAiB,SAAS,2DAA2D;AACtF,YAAG,cAAc,GAAG;AACpB,mBAAU,YAAY,IAAI,cAAc,CAAC;AAAA,UAC3C,CAAC;AACD,UAAG,WAAW,GAAG,2CAA2C,GAAG,CAAC;AAChE,UAAG,aAAa;AAChB,UAAG,WAAW,GAAG,2CAA2C,GAAG,GAAG,OAAO,CAAC,EAAE,GAAG,2CAA2C,GAAG,CAAC,EAAE,GAAG,2CAA2C,GAAG,GAAG,eAAe,MAAM,GAAM,sBAAsB;AAAA,QACvO;AACA,YAAI,KAAK,GAAG;AACV,UAAG,WAAW,UAAU,IAAI,MAAM,EAAE,WAAW,IAAI,OAAO,EAAE,QAAQ,IAAI,IAAI,EAAE,cAAc,IAAI,UAAU,EAAE,wBAAwB,IAAI,oBAAoB,EAAE,eAAe,IAAI,iBAAiB,IAAI,iBAAiB;AACvN,UAAG,UAAU,CAAC;AACd,UAAG,cAAc,GAAG,CAAC,IAAI,UAAU,IAAI,EAAE;AACzC,UAAG,UAAU;AACb,UAAG,cAAc,GAAG,IAAI,SAAS,WAAW,IAAI,CAAC;AAAA,QACnD;AAAA,MACF;AAAA,MACA,cAAc,CAAC,yBAAyB,+BAA+B,wBAAwB,mCAAmC,eAAkB,qBAAwB,gBAAgB;AAAA,MAC5L,eAAe;AAAA,MACf,iBAAiB;AAAA,IACnB,CAAC;AAAA,EACH;AACF;AACA,WAAW,CAAC,aAAa,CAAC,GAAG,mBAAmB,WAAW,UAAU,MAAM;AAC3E,WAAW,CAAC,aAAa,CAAC,GAAG,mBAAmB,WAAW,cAAc,MAAM;AAAA,CAC9E,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,oBAAoB,CAAC;AAAA,IAC3F,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,UAAU;AAAA,MACV,WAAW,CAAC,gBAAgB;AAAA,MAC5B,eAAe,oBAAkB;AAAA,MACjC,iBAAiB,wBAAwB;AAAA,MACzC,qBAAqB;AAAA,MACrB,UAAU;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MA2DV,MAAM;AAAA,QACJ,qCAAqC;AAAA,QACrC,8CAA8C;AAAA,QAC9C,0CAA0C;AAAA,QAC1C,8CAA8C;AAAA,QAC9C,8CAA8C;AAAA,QAC9C,gDAAgD;AAAA,QAChD,4CAA4C;AAAA,QAC5C,4CAA4C;AAAA,QAC5C,4BAA4B;AAAA,QAC5B,qCAAqC;AAAA,QACrC,iCAAiC;AAAA,QACjC,qCAAqC;AAAA,QACrC,qCAAqC;AAAA,QACrC,uCAAuC;AAAA,QACvC,mCAAmC;AAAA,QACnC,mCAAmC;AAAA,QACnC,gCAAgC;AAAA,MAClC;AAAA,MACA,SAAS,CAAC,yBAAyB,+BAA+B,wBAAwB,mCAAmC,aAAa;AAAA,MAC1I,YAAY;AAAA,IACd,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC;AAAA,IACT,MAAM;AAAA,EACR,GAAG;AAAA,IACD,MAAS;AAAA,EACX,GAAG;AAAA,IACD,MAAM;AAAA,EACR,GAAG;AAAA,IACD,MAAW;AAAA,EACb,GAAG;AAAA,IACD,MAAM;AAAA,IACN,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,MACN,MAAM,CAAC,2BAA2B;AAAA,IACpC,CAAC;AAAA,EACH,GAAG;AAAA,IACD,MAAS;AAAA,IACT,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,IACR,CAAC;AAAA,EACH,GAAG;AAAA,IACD,MAAS;AAAA,IACT,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,IACR,GAAG;AAAA,MACD,MAAM;AAAA,IACR,CAAC;AAAA,EACH,CAAC,GAAG;AAAA,IACF,iBAAiB,CAAC;AAAA,MAChB,MAAM;AAAA,IACR,CAAC;AAAA,IACD,eAAe,CAAC;AAAA,MACd,MAAM;AAAA,IACR,CAAC;AAAA,IACD,SAAS,CAAC;AAAA,MACR,MAAM;AAAA,IACR,CAAC;AAAA,IACD,QAAQ,CAAC;AAAA,MACP,MAAM;AAAA,IACR,CAAC;AAAA,IACD,QAAQ,CAAC;AAAA,MACP,MAAM;AAAA,IACR,CAAC;AAAA,IACD,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,IACR,CAAC;AAAA,IACD,aAAa,CAAC;AAAA,MACZ,MAAM;AAAA,IACR,CAAC;AAAA,IACD,cAAc,CAAC;AAAA,MACb,MAAM;AAAA,IACR,CAAC;AAAA,IACD,kBAAkB,CAAC;AAAA,MACjB,MAAM;AAAA,MACN,MAAM,CAAC,kBAAkB;AAAA,QACvB,QAAQ;AAAA,QACR,MAAM;AAAA,MACR,CAAC;AAAA,IACH,CAAC;AAAA,IACD,0BAA0B,CAAC;AAAA,MACzB,MAAM;AAAA,MACN,MAAM,CAAC,WAAW,MAAM,kBAAkB,GAAG;AAAA,QAC3C,aAAa;AAAA,MACf,CAAC;AAAA,IACH,CAAC;AAAA,IACD,2BAA2B,CAAC;AAAA,MAC1B,MAAM;AAAA,MACN,MAAM,CAAC,qBAAqB;AAAA,QAC1B,aAAa;AAAA,MACf,CAAC;AAAA,IACH,CAAC;AAAA,EACH,CAAC;AACH,GAAG;AACH,SAAS,qBAAqB;AAC5B,QAAM,wBAAwB,OAAO,aAAa;AAAA,IAChD,UAAU;AAAA,IACV,UAAU;AAAA,EACZ,CAAC;AACD,QAAM,yBAAyB,OAAO,uBAAuB;AAC7D,SAAO,yBAAyB;AAClC;AACA,SAAS,2BAA2B;AAClC,QAAM,4BAA4B,OAAO,6BAA6B;AAAA,IACpE,UAAU;AAAA,IACV,UAAU;AAAA,EACZ,CAAC;AACD,SAAO,6BAA6B;AACtC;AACA,IAAM,kBAAN,MAAM,iBAAgB;AAAA,EACpB,mBAAmB,iBAAiB;AAClC,SAAK,oBAAoB;AACzB,SAAK,iBAAiB,KAAK,eAAe;AAAA,EAC5C;AAAA,EACA,uBAAuB;AACrB,QAAI,KAAK,2BAA2B;AAClC,UAAI,KAAK,mBAAmB;AAC1B,aAAK,iCAAiC,KAAK,yBAAyB,OAAO,aAAW,QAAQ,MAAM;AACpG,aAAK,yBAAyB,QAAQ,aAAW,QAAQ,4BAA4B,KAAK,CAAC;AAAA,MAC7F,OAAO;AACL,aAAK,+BAA+B,QAAQ,aAAW,QAAQ,4BAA4B,IAAI,CAAC;AAChG,aAAK,iCAAiC,CAAC;AAAA,MACzC;AAAA,IACF;AAAA,EACF;AAAA,EACA,YAAY,eAAe,sBAAsB,KAAK,gBAAgB;AACpE,SAAK,gBAAgB;AACrB,SAAK,uBAAuB;AAC5B,SAAK,MAAM;AACX,SAAK,iBAAiB;AACtB,SAAK,iBAAiB;AACtB,SAAK,UAAU;AACf,SAAK,SAAS;AACd,SAAK,oBAAoB;AACzB,SAAK,eAAe,CAAC,KAAK;AAC1B,SAAK,UAAU,IAAI,aAAa;AAChC,SAAK,aAAa;AAClB,SAAK,MAAM;AACX,SAAK,mBAAmB,IAAI,gBAAgB,KAAK,iBAAiB;AAClE,SAAK,QAAQ,IAAI,gBAAgB,KAAK,MAAM;AAC5C,SAAK,WAAW,IAAI,QAAQ;AAC5B,SAAK,iCAAiC,CAAC;AAAA,EACzC;AAAA,EACA,WAAW;AACT,kBAAc,CAAC,KAAK,kBAAkB,KAAK,KAAK,CAAC,EAAE,KAAK,UAAU,KAAK,QAAQ,CAAC,EAAE,UAAU,CAAC,CAAC,iBAAiB,IAAI,MAAM;AACvH,WAAK,aAAa,kBAAkB,aAAa;AACjD,WAAK,cAAc,QAAQ,KAAK,UAAU;AAC1C,WAAK,IAAI,aAAa;AAAA,IACxB,CAAC;AACD,SAAK,cAAc,yBAAyB,KAAK,UAAU,KAAK,QAAQ,CAAC,EAAE,UAAU,UAAQ;AAC3F,WAAK,QAAQ,KAAK,IAAI;AACtB,UAAI,KAAK,gBAAgB,CAAC,KAAK,eAAe;AAC5C,aAAK,0BAA0B,QAAQ,UAAQ,KAAK,iBAAiB,SAAS,IAAI,CAAC;AAAA,MACrF;AAAA,IACF,CAAC;AACD,SAAK,MAAM,KAAK,eAAe;AAC/B,SAAK,eAAe,QAAQ,KAAK,UAAU,KAAK,QAAQ,CAAC,EAAE,UAAU,eAAa;AAChF,WAAK,MAAM;AACX,WAAK,cAAc,QAAQ,KAAK,UAAU;AAC1C,WAAK,IAAI,aAAa;AAAA,IACxB,CAAC;AAAA,EACH;AAAA,EACA,qBAAqB;AACnB,SAAK,iBAAiB,KAAK,UAAU,KAAK,QAAQ,CAAC,EAAE,UAAU,MAAM;AACnE,WAAK,qBAAqB;AAC1B,WAAK,IAAI,aAAa;AAAA,IACxB,CAAC;AAAA,EACH;AAAA,EACA,YAAY,SAAS;AACnB,UAAM;AAAA,MACJ;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACF,IAAI;AACJ,QAAI,mBAAmB;AACrB,WAAK,iBAAiB,KAAK,KAAK,iBAAiB;AAAA,IACnD;AACA,QAAI,gBAAgB;AAClB,WAAK,cAAc,gBAAgB,KAAK,cAAc;AAAA,IACxD;AACA,QAAI,SAAS;AACX,WAAK,cAAc,SAAS,KAAK,OAAO;AAAA,IAC1C;AACA,QAAI,QAAQ;AACV,WAAK,MAAM,KAAK,KAAK,MAAM;AAC3B,UAAI,CAAC,QAAQ,OAAO,cAAc,KAAK,KAAK,0BAA0B;AACpE,aAAK,yBAAyB,QAAQ,aAAW,QAAQ,4BAA4B,KAAK,CAAC;AAAA,MAC7F;AAAA,IACF;AAAA,EACF;AAAA,EACA,cAAc;AACZ,SAAK,SAAS,KAAK,IAAI;AACvB,SAAK,SAAS,SAAS;AAAA,EACzB;AAAA,EACA,OAAO;AACL,SAAK,OAAO,SAAS,wBAAwB,GAAG;AAC9C,aAAO,KAAK,KAAK,kBAAoB,kBAAkB,WAAW,GAAM,kBAAkB,2BAA2B,GAAM,kBAAqB,iBAAiB,GAAM,kBAAqB,gBAAgB,CAAC,CAAC;AAAA,IAChN;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,OAAyB,kBAAkB;AAAA,MAC9C,MAAM;AAAA,MACN,WAAW,CAAC,CAAC,IAAI,WAAW,EAAE,CAAC;AAAA,MAC/B,gBAAgB,SAAS,+BAA+B,IAAI,KAAK,UAAU;AACzE,YAAI,KAAK,GAAG;AACV,UAAG,eAAe,UAAU,qBAAqB,CAAC;AAClD,UAAG,eAAe,UAAU,oBAAoB,CAAC;AAAA,QACnD;AACA,YAAI,KAAK,GAAG;AACV,cAAI;AACJ,UAAG,eAAe,KAAQ,YAAY,CAAC,MAAM,IAAI,4BAA4B;AAC7E,UAAG,eAAe,KAAQ,YAAY,CAAC,MAAM,IAAI,2BAA2B;AAAA,QAC9E;AAAA,MACF;AAAA,MACA,UAAU;AAAA,MACV,cAAc,SAAS,6BAA6B,IAAI,KAAK;AAC3D,YAAI,KAAK,GAAG;AACV,UAAG,YAAY,qBAAqB,IAAI,oBAAoB,EAAE,0BAA0B,IAAI,oBAAoB,EAAE,2BAA2B,IAAI,wBAAwB,IAAI,YAAY,OAAO,EAAE,0BAA0B,IAAI,wBAAwB,IAAI,YAAY,MAAM,EAAE,8BAA8B,IAAI,wBAAwB,IAAI,eAAe,UAAU,EAAE,gCAAgC,IAAI,wBAAwB,IAAI,eAAe,YAAY,EAAE,4BAA4B,IAAI,wBAAwB,IAAI,eAAe,QAAQ,EAAE,sCAAsC,IAAI,wBAAwB,IAAI,iBAAiB,EAAE,YAAY,CAAC,IAAI,oBAAoB,EAAE,iBAAiB,CAAC,IAAI,oBAAoB,EAAE,kBAAkB,CAAC,IAAI,wBAAwB,IAAI,YAAY,OAAO,EAAE,iBAAiB,CAAC,IAAI,wBAAwB,IAAI,YAAY,MAAM,EAAE,qBAAqB,CAAC,IAAI,wBAAwB,IAAI,eAAe,UAAU,EAAE,uBAAuB,CAAC,IAAI,wBAAwB,IAAI,eAAe,YAAY,EAAE,mBAAmB,CAAC,IAAI,wBAAwB,IAAI,eAAe,QAAQ,EAAE,6BAA6B,CAAC,IAAI,wBAAwB,IAAI,iBAAiB,EAAE,gBAAgB,IAAI,QAAQ,KAAK;AAAA,QAC5rC;AAAA,MACF;AAAA,MACA,QAAQ;AAAA,QACN,gBAAgB;AAAA,QAChB,SAAS;AAAA,QACT,QAAQ;AAAA,QACR,mBAAmB;AAAA,QACnB,cAAc;AAAA,MAChB;AAAA,MACA,SAAS;AAAA,QACP,SAAS;AAAA,MACX;AAAA,MACA,UAAU,CAAC,QAAQ;AAAA,MACnB,YAAY;AAAA,MACZ,UAAU,CAAI,mBAAmB;AAAA,QAAC;AAAA,UAChC,SAAS;AAAA,UACT,UAAU;AAAA,QACZ;AAAA;AAAA,QACA;AAAA,UACE,SAAS;AAAA,UACT,YAAY;AAAA,QACd;AAAA;AAAA,QACA;AAAA,UACE,SAAS;AAAA,UACT,YAAY;AAAA,QACd;AAAA,MAAC,CAAC,GAAM,oBAAoB;AAAA,IAC9B,CAAC;AAAA,EACH;AACF;AACA,WAAW,CAAC,aAAa,CAAC,GAAG,gBAAgB,WAAW,qBAAqB,MAAM;AACnF,WAAW,CAAC,aAAa,CAAC,GAAG,gBAAgB,WAAW,gBAAgB,MAAM;AAAA,CAC7E,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,iBAAiB,CAAC;AAAA,IACxF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,UAAU;AAAA,MACV,WAAW;AAAA,QAAC;AAAA,UACV,SAAS;AAAA,UACT,UAAU;AAAA,QACZ;AAAA;AAAA,QACA;AAAA,UACE,SAAS;AAAA,UACT,YAAY;AAAA,QACd;AAAA;AAAA,QACA;AAAA,UACE,SAAS;AAAA,UACT,YAAY;AAAA,QACd;AAAA,MAAC;AAAA,MACD,MAAM;AAAA,QACJ,6BAA6B;AAAA,QAC7B,kCAAkC;AAAA,QAClC,mCAAmC;AAAA,QACnC,kCAAkC;AAAA,QAClC,sCAAsC;AAAA,QACtC,wCAAwC;AAAA,QACxC,oCAAoC;AAAA,QACpC,8CAA8C;AAAA,QAC9C,oBAAoB;AAAA,QACpB,yBAAyB;AAAA,QACzB,0BAA0B;AAAA,QAC1B,yBAAyB;AAAA,QACzB,6BAA6B;AAAA,QAC7B,+BAA+B;AAAA,QAC/B,2BAA2B;AAAA,QAC3B,qCAAqC;AAAA,QACrC,wBAAwB;AAAA,MAC1B;AAAA,MACA,YAAY;AAAA,IACd,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC;AAAA,IACT,MAAM;AAAA,EACR,GAAG;AAAA,IACD,MAAM;AAAA,IACN,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,MACN,MAAM,CAAC,2BAA2B;AAAA,IACpC,CAAC;AAAA,EACH,GAAG;AAAA,IACD,MAAS;AAAA,EACX,GAAG;AAAA,IACD,MAAS;AAAA,IACT,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,IACR,CAAC;AAAA,EACH,CAAC,GAAG;AAAA,IACF,2BAA2B,CAAC;AAAA,MAC1B,MAAM;AAAA,MACN,MAAM,CAAC,qBAAqB;AAAA,QAC1B,aAAa;AAAA,MACf,CAAC;AAAA,IACH,CAAC;AAAA,IACD,0BAA0B,CAAC;AAAA,MACzB,MAAM;AAAA,MACN,MAAM,CAAC,oBAAoB;AAAA,QACzB,aAAa;AAAA,MACf,CAAC;AAAA,IACH,CAAC;AAAA,IACD,gBAAgB,CAAC;AAAA,MACf,MAAM;AAAA,IACR,CAAC;AAAA,IACD,SAAS,CAAC;AAAA,MACR,MAAM;AAAA,IACR,CAAC;AAAA,IACD,QAAQ,CAAC;AAAA,MACP,MAAM;AAAA,IACR,CAAC;AAAA,IACD,mBAAmB,CAAC;AAAA,MAClB,MAAM;AAAA,IACR,CAAC;AAAA,IACD,cAAc,CAAC;AAAA,MACb,MAAM;AAAA,IACR,CAAC;AAAA,IACD,SAAS,CAAC;AAAA,MACR,MAAM;AAAA,IACR,CAAC;AAAA,EACH,CAAC;AACH,GAAG;AAMH,SAAS,mBAAmB;AAC1B,QAAM,4BAA4B,OAAO,6BAA6B;AAAA,IACpE,UAAU;AAAA,IACV,UAAU;AAAA,EACZ,CAAC;AACD,SAAO,6BAA6B;AACtC;AACA,IAAM,uBAAN,MAAM,sBAAqB;AAAA,EACzB,YAAY,YAAY,UAAU,sBAAsB;AACtD,SAAK,aAAa;AAClB,SAAK,WAAW;AAChB,SAAK,uBAAuB;AAC5B,UAAM,YAAY,KAAK,uBAAuB,iCAAiC;AAC/E,SAAK,SAAS,SAAS,WAAW,eAAe,SAAS;AAAA,EAC5D;AAAA,EACA,kBAAkB;AAChB,UAAM,YAAY,KAAK,aAAa,cAAc;AAClD,QAAI,WAAW;AAEb,YAAM,YAAY,KAAK,uBAAuB,sCAAsC;AACpF,WAAK,SAAS,SAAS,WAAW,SAAS;AAAA,IAC7C;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,OAAO,SAAS,6BAA6B,GAAG;AACnD,aAAO,KAAK,KAAK,uBAAyB,kBAAqB,UAAU,GAAM,kBAAqB,SAAS,GAAM,kBAAkB,2BAA2B,CAAC;AAAA,IACnK;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,OAAyB,kBAAkB;AAAA,MAC9C,MAAM;AAAA,MACN,WAAW,CAAC,CAAC,IAAI,iBAAiB,EAAE,CAAC;AAAA,MACrC,WAAW,SAAS,2BAA2B,IAAI,KAAK;AACtD,YAAI,KAAK,GAAG;AACV,UAAG,YAAY,KAAK,CAAC;AAAA,QACvB;AACA,YAAI,KAAK,GAAG;AACV,cAAI;AACJ,UAAG,eAAe,KAAQ,YAAY,CAAC,MAAM,IAAI,eAAe,GAAG;AAAA,QACrE;AAAA,MACF;AAAA,MACA,QAAQ;AAAA,QACN,SAAS;AAAA,MACX;AAAA,MACA,UAAU,CAAC,aAAa;AAAA,MACxB,YAAY;AAAA,MACZ,UAAU,CAAI,mBAAmB;AAAA;AAAA,QACjC;AAAA,UACE,SAAS;AAAA,UACT,YAAY;AAAA,QACd;AAAA,MAAC,CAAC,GAAM,mBAAmB;AAAA,MAC3B,OAAO;AAAA,MACP,oBAAoB;AAAA,MACpB,OAAO;AAAA,MACP,MAAM;AAAA,MACN,QAAQ,CAAC,CAAC,gBAAgB,EAAE,GAAG,CAAC,GAAG,wBAAwB,CAAC;AAAA,MAC5D,UAAU,SAAS,8BAA8B,IAAI,KAAK;AACxD,YAAI,KAAK,GAAG;AACV,UAAG,gBAAgB,IAAI;AACvB,UAAG,eAAe,GAAG,OAAO,MAAM,CAAC;AACnC,UAAG,WAAW,GAAG,8CAA8C,GAAG,GAAG,gBAAgB,CAAC,EAAE,GAAG,6CAA6C,GAAG,CAAC;AAC5I,UAAG,aAAa;AAChB,UAAG,aAAa,CAAC;AAAA,QACnB;AACA,YAAI,KAAK,GAAG;AACV,UAAG,YAAY,6BAA6B,CAAC,IAAI,oBAAoB,EAAE,sCAAsC,IAAI,oBAAoB;AACrI,UAAG,UAAU,CAAC;AACd,UAAG,WAAW,0BAA0B,IAAI,OAAO;AACnD,UAAG,UAAU;AACb,UAAG,cAAc,GAAG,CAAC,IAAI,UAAU,IAAI,EAAE;AAAA,QAC3C;AAAA,MACF;AAAA,MACA,cAAc,CAAC,gBAAmB,+BAA+B;AAAA,MACjE,eAAe;AAAA,MACf,iBAAiB;AAAA,IACnB,CAAC;AAAA,EACH;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,sBAAsB,CAAC;AAAA,IAC7F,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,UAAU;AAAA,MACV,iBAAiB,wBAAwB;AAAA,MACzC,WAAW;AAAA;AAAA,QACX;AAAA,UACE,SAAS;AAAA,UACT,YAAY;AAAA,QACd;AAAA,MAAC;AAAA,MACD,eAAe,oBAAkB;AAAA,MACjC,UAAU;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MAaV,qBAAqB;AAAA,MACrB,SAAS,CAAC,cAAc;AAAA,MACxB,YAAY;AAAA,IACd,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC;AAAA,IACT,MAAS;AAAA,EACX,GAAG;AAAA,IACD,MAAS;AAAA,EACX,GAAG;AAAA,IACD,MAAM;AAAA,IACN,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,MACN,MAAM,CAAC,2BAA2B;AAAA,IACpC,CAAC;AAAA,EACH,CAAC,GAAG;AAAA,IACF,SAAS,CAAC;AAAA,MACR,MAAM;AAAA,IACR,CAAC;AAAA,IACD,cAAc,CAAC;AAAA,MACb,MAAM;AAAA,MACN,MAAM,CAAC,cAAc;AAAA,IACvB,CAAC;AAAA,EACH,CAAC;AACH,GAAG;AAMH,IAAM,yBAAN,MAAM,wBAAuB;AAAA,EAC3B,YAAY,YAAY;AACtB,SAAK,aAAa;AAAA,EACpB;AAAA,EACA,OAAO;AACL,SAAK,OAAO,SAAS,+BAA+B,GAAG;AACrD,aAAO,KAAK,KAAK,yBAA2B,kBAAqB,UAAU,CAAC;AAAA,IAC9E;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,OAAyB,kBAAkB;AAAA,MAC9C,MAAM;AAAA,MACN,WAAW,CAAC,CAAC,IAAI,mBAAmB,EAAE,CAAC;AAAA,MACvC,WAAW,CAAC,GAAG,gCAAgC;AAAA,MAC/C,UAAU,CAAC,eAAe;AAAA,MAC1B,YAAY;AAAA,IACd,CAAC;AAAA,EACH;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,wBAAwB,CAAC;AAAA,IAC/F,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,UAAU;AAAA,MACV,MAAM;AAAA,QACJ,OAAO;AAAA,MACT;AAAA,MACA,YAAY;AAAA,IACd,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC;AAAA,IACT,MAAS;AAAA,EACX,CAAC,GAAG,IAAI;AACV,GAAG;AAMH,IAAM,eAAN,MAAM,cAAa;AAAA,EACjB,OAAO;AACL,SAAK,OAAO,SAAS,qBAAqB,GAAG;AAC3C,aAAO,KAAK,KAAK,eAAc;AAAA,IACjC;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,OAAyB,iBAAiB;AAAA,MAC7C,MAAM;AAAA,MACN,SAAS,CAAC,iBAAiB,qBAAqB,oBAAoB,wBAAwB,sBAAsB,yBAAyB,+BAA+B,iCAAiC;AAAA,MAC3M,SAAS,CAAC,iBAAiB,qBAAqB,oBAAoB,wBAAwB,oBAAoB;AAAA,IAClH,CAAC;AAAA,EACH;AAAA,EACA,OAAO;AACL,SAAK,OAAyB,iBAAiB;AAAA,MAC7C,SAAS,CAAC,oBAAoB,sBAAsB,uBAAuB;AAAA,IAC7E,CAAC;AAAA,EACH;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,cAAc,CAAC;AAAA,IACrF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,SAAS,CAAC,iBAAiB,qBAAqB,oBAAoB,wBAAwB,sBAAsB,yBAAyB,+BAA+B,iCAAiC;AAAA,MAC3M,SAAS,CAAC,iBAAiB,qBAAqB,oBAAoB,wBAAwB,oBAAoB;AAAA,IAClH,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;", "names": []}