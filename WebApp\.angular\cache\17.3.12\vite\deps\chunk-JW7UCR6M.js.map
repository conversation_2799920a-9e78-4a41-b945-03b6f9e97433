{"version": 3, "sources": ["../../../../../node_modules/ng-zorro-antd/fesm2022/ng-zorro-antd-dropdown.mjs"], "sourcesContent": ["import { __decorate } from 'tslib';\nimport { ESCAPE, hasModifierKey } from '@angular/cdk/keycodes';\nimport { TemplatePortal } from '@angular/cdk/portal';\nimport * as i0 from '@angular/core';\nimport { EventEmitter, Directive, Input, Output, NgModule, Host, Optional, TemplateRef, Component, ViewEncapsulation, ChangeDetectionStrategy, ViewChild, Injectable } from '@angular/core';\nimport { Subject, BehaviorSubject, merge, fromEvent, EMPTY, combineLatest, Subscription } from 'rxjs';\nimport { mapTo, map, switchMap, filter, auditTime, distinctUntilChanged, takeUntil, first } from 'rxjs/operators';\nimport * as i1 from 'ng-zorro-antd/core/config';\nimport { WithConfig } from 'ng-zorro-antd/core/config';\nimport { POSITION_MAP } from 'ng-zorro-antd/core/overlay';\nimport { InputBoolean } from 'ng-zorro-antd/core/util';\nimport * as i2 from '@angular/cdk/overlay';\nimport { ConnectionPositionPair } from '@angular/cdk/overlay';\nimport * as i3 from '@angular/cdk/platform';\nimport * as i1$2 from 'ng-zorro-antd/menu';\nimport { MenuService, NzIsMenuInsideDropDownToken, NzMenuModule } from 'ng-zorro-antd/menu';\nimport * as i1$1 from 'ng-zorro-antd/button';\nimport { NgClass, NgStyle } from '@angular/common';\nimport { slideMotion } from 'ng-zorro-antd/core/animation';\nimport * as i3$1 from 'ng-zorro-antd/core/no-animation';\nimport { NzNoAnimationDirective } from 'ng-zorro-antd/core/no-animation';\nimport * as i2$1 from '@angular/cdk/bidi';\nconst _c0 = [\"*\"];\nfunction NzDropdownMenuComponent_ng_template_0_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 0);\n    i0.ɵɵlistener(\"@slideMotion.done\", function NzDropdownMenuComponent_ng_template_0_Template_div_animation_slideMotion_done_0_listener($event) {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.onAnimationEvent($event));\n    })(\"mouseenter\", function NzDropdownMenuComponent_ng_template_0_Template_div_mouseenter_0_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.setMouseState(true));\n    })(\"mouseleave\", function NzDropdownMenuComponent_ng_template_0_Template_div_mouseleave_0_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.setMouseState(false));\n    });\n    i0.ɵɵprojection(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵclassProp(\"ant-dropdown-rtl\", ctx_r1.dir === \"rtl\");\n    i0.ɵɵproperty(\"ngClass\", ctx_r1.nzOverlayClassName)(\"ngStyle\", ctx_r1.nzOverlayStyle)(\"@slideMotion\", undefined)(\"@.disabled\", !!(ctx_r1.noAnimation == null ? null : ctx_r1.noAnimation.nzNoAnimation))(\"nzNoAnimation\", ctx_r1.noAnimation == null ? null : ctx_r1.noAnimation.nzNoAnimation);\n  }\n}\nconst NZ_CONFIG_MODULE_NAME = 'dropDown';\nconst listOfPositions = [POSITION_MAP.bottomLeft, POSITION_MAP.bottomRight, POSITION_MAP.topRight, POSITION_MAP.topLeft];\nclass NzDropDownDirective {\n  setDropdownMenuValue(key, value) {\n    if (this.nzDropdownMenu) {\n      this.nzDropdownMenu.setValue(key, value);\n    }\n  }\n  constructor(nzConfigService, elementRef, overlay, renderer, viewContainerRef, platform) {\n    this.nzConfigService = nzConfigService;\n    this.elementRef = elementRef;\n    this.overlay = overlay;\n    this.renderer = renderer;\n    this.viewContainerRef = viewContainerRef;\n    this.platform = platform;\n    this._nzModuleName = NZ_CONFIG_MODULE_NAME;\n    this.overlayRef = null;\n    this.destroy$ = new Subject();\n    this.positionStrategy = this.overlay.position().flexibleConnectedTo(this.elementRef.nativeElement).withLockedPosition().withTransformOriginOn('.ant-dropdown');\n    this.inputVisible$ = new BehaviorSubject(false);\n    this.nzTrigger$ = new BehaviorSubject('hover');\n    this.overlayClose$ = new Subject();\n    this.nzDropdownMenu = null;\n    this.nzTrigger = 'hover';\n    this.nzMatchWidthElement = null;\n    this.nzBackdrop = false;\n    this.nzClickHide = true;\n    this.nzDisabled = false;\n    this.nzVisible = false;\n    this.nzOverlayClassName = '';\n    this.nzOverlayStyle = {};\n    this.nzPlacement = 'bottomLeft';\n    this.nzVisibleChange = new EventEmitter();\n  }\n  ngAfterViewInit() {\n    if (this.nzDropdownMenu) {\n      const nativeElement = this.elementRef.nativeElement;\n      /** host mouse state **/\n      const hostMouseState$ = merge(fromEvent(nativeElement, 'mouseenter').pipe(mapTo(true)), fromEvent(nativeElement, 'mouseleave').pipe(mapTo(false)));\n      /** menu mouse state **/\n      const menuMouseState$ = this.nzDropdownMenu.mouseState$;\n      /** merged mouse state **/\n      const mergedMouseState$ = merge(menuMouseState$, hostMouseState$);\n      /** host click state **/\n      const hostClickState$ = fromEvent(nativeElement, 'click').pipe(map(() => !this.nzVisible));\n      /** visible state switch by nzTrigger **/\n      const visibleStateByTrigger$ = this.nzTrigger$.pipe(switchMap(trigger => {\n        if (trigger === 'hover') {\n          return mergedMouseState$;\n        } else if (trigger === 'click') {\n          return hostClickState$;\n        } else {\n          return EMPTY;\n        }\n      }));\n      const descendantMenuItemClick$ = this.nzDropdownMenu.descendantMenuItemClick$.pipe(filter(() => this.nzClickHide), mapTo(false));\n      const domTriggerVisible$ = merge(visibleStateByTrigger$, descendantMenuItemClick$, this.overlayClose$).pipe(filter(() => !this.nzDisabled));\n      const visible$ = merge(this.inputVisible$, domTriggerVisible$);\n      combineLatest([visible$, this.nzDropdownMenu.isChildSubMenuOpen$]).pipe(map(([visible, sub]) => visible || sub), auditTime(150), distinctUntilChanged(), filter(() => this.platform.isBrowser), takeUntil(this.destroy$)).subscribe(visible => {\n        const element = this.nzMatchWidthElement ? this.nzMatchWidthElement.nativeElement : nativeElement;\n        const triggerWidth = element.getBoundingClientRect().width;\n        if (this.nzVisible !== visible) {\n          this.nzVisibleChange.emit(visible);\n        }\n        this.nzVisible = visible;\n        if (visible) {\n          /** set up overlayRef **/\n          if (!this.overlayRef) {\n            /** new overlay **/\n            this.overlayRef = this.overlay.create({\n              positionStrategy: this.positionStrategy,\n              minWidth: triggerWidth,\n              disposeOnNavigation: true,\n              hasBackdrop: this.nzBackdrop && this.nzTrigger === 'click',\n              scrollStrategy: this.overlay.scrollStrategies.reposition()\n            });\n            merge(this.overlayRef.backdropClick(), this.overlayRef.detachments(), this.overlayRef.outsidePointerEvents().pipe(filter(e => !this.elementRef.nativeElement.contains(e.target))), this.overlayRef.keydownEvents().pipe(filter(e => e.keyCode === ESCAPE && !hasModifierKey(e)))).pipe(takeUntil(this.destroy$)).subscribe(() => {\n              this.overlayClose$.next(false);\n            });\n          } else {\n            /** update overlay config **/\n            const overlayConfig = this.overlayRef.getConfig();\n            overlayConfig.minWidth = triggerWidth;\n          }\n          /** open dropdown with animation **/\n          this.positionStrategy.withPositions([POSITION_MAP[this.nzPlacement], ...listOfPositions]);\n          /** reset portal if needed **/\n          if (!this.portal || this.portal.templateRef !== this.nzDropdownMenu.templateRef) {\n            this.portal = new TemplatePortal(this.nzDropdownMenu.templateRef, this.viewContainerRef);\n          }\n          this.overlayRef.attach(this.portal);\n        } else {\n          /** detach overlayRef if needed **/\n          if (this.overlayRef) {\n            this.overlayRef.detach();\n          }\n        }\n      });\n      this.nzDropdownMenu.animationStateChange$.pipe(takeUntil(this.destroy$)).subscribe(event => {\n        if (event.toState === 'void') {\n          if (this.overlayRef) {\n            this.overlayRef.dispose();\n          }\n          this.overlayRef = null;\n        }\n      });\n    }\n  }\n  ngOnDestroy() {\n    this.destroy$.next(true);\n    this.destroy$.complete();\n    if (this.overlayRef) {\n      this.overlayRef.dispose();\n      this.overlayRef = null;\n    }\n  }\n  ngOnChanges(changes) {\n    const {\n      nzVisible,\n      nzDisabled,\n      nzOverlayClassName,\n      nzOverlayStyle,\n      nzTrigger\n    } = changes;\n    if (nzTrigger) {\n      this.nzTrigger$.next(this.nzTrigger);\n    }\n    if (nzVisible) {\n      this.inputVisible$.next(this.nzVisible);\n    }\n    if (nzDisabled) {\n      const nativeElement = this.elementRef.nativeElement;\n      if (this.nzDisabled) {\n        this.renderer.setAttribute(nativeElement, 'disabled', '');\n        this.inputVisible$.next(false);\n      } else {\n        this.renderer.removeAttribute(nativeElement, 'disabled');\n      }\n    }\n    if (nzOverlayClassName) {\n      this.setDropdownMenuValue('nzOverlayClassName', this.nzOverlayClassName);\n    }\n    if (nzOverlayStyle) {\n      this.setDropdownMenuValue('nzOverlayStyle', this.nzOverlayStyle);\n    }\n  }\n  static {\n    this.ɵfac = function NzDropDownDirective_Factory(t) {\n      return new (t || NzDropDownDirective)(i0.ɵɵdirectiveInject(i1.NzConfigService), i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(i2.Overlay), i0.ɵɵdirectiveInject(i0.Renderer2), i0.ɵɵdirectiveInject(i0.ViewContainerRef), i0.ɵɵdirectiveInject(i3.Platform));\n    };\n  }\n  static {\n    this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: NzDropDownDirective,\n      selectors: [[\"\", \"nz-dropdown\", \"\"]],\n      hostAttrs: [1, \"ant-dropdown-trigger\"],\n      inputs: {\n        nzDropdownMenu: \"nzDropdownMenu\",\n        nzTrigger: \"nzTrigger\",\n        nzMatchWidthElement: \"nzMatchWidthElement\",\n        nzBackdrop: \"nzBackdrop\",\n        nzClickHide: \"nzClickHide\",\n        nzDisabled: \"nzDisabled\",\n        nzVisible: \"nzVisible\",\n        nzOverlayClassName: \"nzOverlayClassName\",\n        nzOverlayStyle: \"nzOverlayStyle\",\n        nzPlacement: \"nzPlacement\"\n      },\n      outputs: {\n        nzVisibleChange: \"nzVisibleChange\"\n      },\n      exportAs: [\"nzDropdown\"],\n      standalone: true,\n      features: [i0.ɵɵNgOnChangesFeature]\n    });\n  }\n}\n__decorate([WithConfig(), InputBoolean()], NzDropDownDirective.prototype, \"nzBackdrop\", void 0);\n__decorate([InputBoolean()], NzDropDownDirective.prototype, \"nzClickHide\", void 0);\n__decorate([InputBoolean()], NzDropDownDirective.prototype, \"nzDisabled\", void 0);\n__decorate([InputBoolean()], NzDropDownDirective.prototype, \"nzVisible\", void 0);\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(NzDropDownDirective, [{\n    type: Directive,\n    args: [{\n      selector: '[nz-dropdown]',\n      exportAs: 'nzDropdown',\n      host: {\n        class: 'ant-dropdown-trigger'\n      },\n      standalone: true\n    }]\n  }], () => [{\n    type: i1.NzConfigService\n  }, {\n    type: i0.ElementRef\n  }, {\n    type: i2.Overlay\n  }, {\n    type: i0.Renderer2\n  }, {\n    type: i0.ViewContainerRef\n  }, {\n    type: i3.Platform\n  }], {\n    nzDropdownMenu: [{\n      type: Input\n    }],\n    nzTrigger: [{\n      type: Input\n    }],\n    nzMatchWidthElement: [{\n      type: Input\n    }],\n    nzBackdrop: [{\n      type: Input\n    }],\n    nzClickHide: [{\n      type: Input\n    }],\n    nzDisabled: [{\n      type: Input\n    }],\n    nzVisible: [{\n      type: Input\n    }],\n    nzOverlayClassName: [{\n      type: Input\n    }],\n    nzOverlayStyle: [{\n      type: Input\n    }],\n    nzPlacement: [{\n      type: Input\n    }],\n    nzVisibleChange: [{\n      type: Output\n    }]\n  });\n})();\n\n/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\nclass NzContextMenuServiceModule {\n  static {\n    this.ɵfac = function NzContextMenuServiceModule_Factory(t) {\n      return new (t || NzContextMenuServiceModule)();\n    };\n  }\n  static {\n    this.ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n      type: NzContextMenuServiceModule\n    });\n  }\n  static {\n    this.ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({});\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(NzContextMenuServiceModule, [{\n    type: NgModule\n  }], null, null);\n})();\n\n/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\nclass NzDropDownADirective {\n  constructor() {}\n  static {\n    this.ɵfac = function NzDropDownADirective_Factory(t) {\n      return new (t || NzDropDownADirective)();\n    };\n  }\n  static {\n    this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: NzDropDownADirective,\n      selectors: [[\"a\", \"nz-dropdown\", \"\"]],\n      hostAttrs: [1, \"ant-dropdown-link\"],\n      standalone: true\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(NzDropDownADirective, [{\n    type: Directive,\n    args: [{\n      selector: 'a[nz-dropdown]',\n      host: {\n        class: 'ant-dropdown-link'\n      },\n      standalone: true\n    }]\n  }], () => [], null);\n})();\n\n/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\nclass NzDropdownButtonDirective {\n  constructor(renderer, nzButtonGroupComponent, elementRef) {\n    this.renderer = renderer;\n    this.nzButtonGroupComponent = nzButtonGroupComponent;\n    this.elementRef = elementRef;\n  }\n  ngAfterViewInit() {\n    const parentElement = this.renderer.parentNode(this.elementRef.nativeElement);\n    if (this.nzButtonGroupComponent && parentElement) {\n      this.renderer.addClass(parentElement, 'ant-dropdown-button');\n    }\n  }\n  static {\n    this.ɵfac = function NzDropdownButtonDirective_Factory(t) {\n      return new (t || NzDropdownButtonDirective)(i0.ɵɵdirectiveInject(i0.Renderer2), i0.ɵɵdirectiveInject(i1$1.NzButtonGroupComponent, 9), i0.ɵɵdirectiveInject(i0.ElementRef));\n    };\n  }\n  static {\n    this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: NzDropdownButtonDirective,\n      selectors: [[\"\", \"nz-button\", \"\", \"nz-dropdown\", \"\"]],\n      standalone: true\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(NzDropdownButtonDirective, [{\n    type: Directive,\n    args: [{\n      selector: '[nz-button][nz-dropdown]',\n      standalone: true\n    }]\n  }], () => [{\n    type: i0.Renderer2\n  }, {\n    type: i1$1.NzButtonGroupComponent,\n    decorators: [{\n      type: Host\n    }, {\n      type: Optional\n    }]\n  }, {\n    type: i0.ElementRef\n  }], null);\n})();\nclass NzDropdownMenuComponent {\n  onAnimationEvent(event) {\n    this.animationStateChange$.emit(event);\n  }\n  setMouseState(visible) {\n    this.mouseState$.next(visible);\n  }\n  setValue(key, value) {\n    this[key] = value;\n    this.cdr.markForCheck();\n  }\n  constructor(cdr, elementRef, renderer, viewContainerRef, nzMenuService, directionality, noAnimation) {\n    this.cdr = cdr;\n    this.elementRef = elementRef;\n    this.renderer = renderer;\n    this.viewContainerRef = viewContainerRef;\n    this.nzMenuService = nzMenuService;\n    this.directionality = directionality;\n    this.noAnimation = noAnimation;\n    this.mouseState$ = new BehaviorSubject(false);\n    this.isChildSubMenuOpen$ = this.nzMenuService.isChildSubMenuOpen$;\n    this.descendantMenuItemClick$ = this.nzMenuService.descendantMenuItemClick$;\n    this.animationStateChange$ = new EventEmitter();\n    this.nzOverlayClassName = '';\n    this.nzOverlayStyle = {};\n    this.dir = 'ltr';\n    this.destroy$ = new Subject();\n  }\n  ngOnInit() {\n    this.directionality.change?.pipe(takeUntil(this.destroy$)).subscribe(direction => {\n      this.dir = direction;\n      this.cdr.detectChanges();\n    });\n    this.dir = this.directionality.value;\n  }\n  ngAfterContentInit() {\n    this.renderer.removeChild(this.renderer.parentNode(this.elementRef.nativeElement), this.elementRef.nativeElement);\n  }\n  ngOnDestroy() {\n    this.destroy$.next();\n    this.destroy$.complete();\n  }\n  static {\n    this.ɵfac = function NzDropdownMenuComponent_Factory(t) {\n      return new (t || NzDropdownMenuComponent)(i0.ɵɵdirectiveInject(i0.ChangeDetectorRef), i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(i0.Renderer2), i0.ɵɵdirectiveInject(i0.ViewContainerRef), i0.ɵɵdirectiveInject(i1$2.MenuService), i0.ɵɵdirectiveInject(i2$1.Directionality, 8), i0.ɵɵdirectiveInject(i3$1.NzNoAnimationDirective, 9));\n    };\n  }\n  static {\n    this.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n      type: NzDropdownMenuComponent,\n      selectors: [[\"nz-dropdown-menu\"]],\n      viewQuery: function NzDropdownMenuComponent_Query(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵviewQuery(TemplateRef, 7);\n        }\n        if (rf & 2) {\n          let _t;\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.templateRef = _t.first);\n        }\n      },\n      exportAs: [\"nzDropdownMenu\"],\n      standalone: true,\n      features: [i0.ɵɵProvidersFeature([MenuService, /** menu is inside dropdown-menu component **/\n      {\n        provide: NzIsMenuInsideDropDownToken,\n        useValue: true\n      }]), i0.ɵɵStandaloneFeature],\n      ngContentSelectors: _c0,\n      decls: 1,\n      vars: 0,\n      consts: [[1, \"ant-dropdown\", 3, \"mouseenter\", \"mouseleave\", \"ngClass\", \"ngStyle\", \"nzNoAnimation\"]],\n      template: function NzDropdownMenuComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵprojectionDef();\n          i0.ɵɵtemplate(0, NzDropdownMenuComponent_ng_template_0_Template, 2, 7, \"ng-template\");\n        }\n      },\n      dependencies: [NgClass, NgStyle, NzNoAnimationDirective],\n      encapsulation: 2,\n      data: {\n        animation: [slideMotion]\n      },\n      changeDetection: 0\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(NzDropdownMenuComponent, [{\n    type: Component,\n    args: [{\n      selector: `nz-dropdown-menu`,\n      exportAs: `nzDropdownMenu`,\n      animations: [slideMotion],\n      providers: [MenuService, /** menu is inside dropdown-menu component **/\n      {\n        provide: NzIsMenuInsideDropDownToken,\n        useValue: true\n      }],\n      template: `\n    <ng-template>\n      <div\n        class=\"ant-dropdown\"\n        [class.ant-dropdown-rtl]=\"dir === 'rtl'\"\n        [ngClass]=\"nzOverlayClassName\"\n        [ngStyle]=\"nzOverlayStyle\"\n        @slideMotion\n        (@slideMotion.done)=\"onAnimationEvent($event)\"\n        [@.disabled]=\"!!noAnimation?.nzNoAnimation\"\n        [nzNoAnimation]=\"noAnimation?.nzNoAnimation\"\n        (mouseenter)=\"setMouseState(true)\"\n        (mouseleave)=\"setMouseState(false)\"\n      >\n        <ng-content></ng-content>\n      </div>\n    </ng-template>\n  `,\n      preserveWhitespaces: false,\n      encapsulation: ViewEncapsulation.None,\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      imports: [NgClass, NgStyle, NzNoAnimationDirective],\n      standalone: true\n    }]\n  }], () => [{\n    type: i0.ChangeDetectorRef\n  }, {\n    type: i0.ElementRef\n  }, {\n    type: i0.Renderer2\n  }, {\n    type: i0.ViewContainerRef\n  }, {\n    type: i1$2.MenuService\n  }, {\n    type: i2$1.Directionality,\n    decorators: [{\n      type: Optional\n    }]\n  }, {\n    type: i3$1.NzNoAnimationDirective,\n    decorators: [{\n      type: Host\n    }, {\n      type: Optional\n    }]\n  }], {\n    templateRef: [{\n      type: ViewChild,\n      args: [TemplateRef, {\n        static: true\n      }]\n    }]\n  });\n})();\n\n/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\nclass NzDropDownModule {\n  static {\n    this.ɵfac = function NzDropDownModule_Factory(t) {\n      return new (t || NzDropDownModule)();\n    };\n  }\n  static {\n    this.ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n      type: NzDropDownModule,\n      imports: [NzDropDownDirective, NzDropDownADirective, NzDropdownMenuComponent, NzDropdownButtonDirective, NzContextMenuServiceModule],\n      exports: [NzMenuModule, NzDropDownDirective, NzDropDownADirective, NzDropdownMenuComponent, NzDropdownButtonDirective]\n    });\n  }\n  static {\n    this.ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n      imports: [NzContextMenuServiceModule, NzMenuModule]\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(NzDropDownModule, [{\n    type: NgModule,\n    args: [{\n      imports: [NzDropDownDirective, NzDropDownADirective, NzDropdownMenuComponent, NzDropdownButtonDirective, NzContextMenuServiceModule],\n      exports: [NzMenuModule, NzDropDownDirective, NzDropDownADirective, NzDropdownMenuComponent, NzDropdownButtonDirective]\n    }]\n  }], null, null);\n})();\n\n/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\nconst LIST_OF_POSITIONS = [new ConnectionPositionPair({\n  originX: 'start',\n  originY: 'top'\n}, {\n  overlayX: 'start',\n  overlayY: 'top'\n}), new ConnectionPositionPair({\n  originX: 'start',\n  originY: 'top'\n}, {\n  overlayX: 'start',\n  overlayY: 'bottom'\n}), new ConnectionPositionPair({\n  originX: 'start',\n  originY: 'top'\n}, {\n  overlayX: 'end',\n  overlayY: 'bottom'\n}), new ConnectionPositionPair({\n  originX: 'start',\n  originY: 'top'\n}, {\n  overlayX: 'end',\n  overlayY: 'top'\n})];\nclass NzContextMenuService {\n  constructor(ngZone, overlay) {\n    this.ngZone = ngZone;\n    this.overlay = overlay;\n    this.overlayRef = null;\n    this.closeSubscription = Subscription.EMPTY;\n  }\n  create($event, nzDropdownMenuComponent) {\n    this.close(true);\n    const {\n      x,\n      y\n    } = $event;\n    if ($event instanceof MouseEvent) {\n      $event.preventDefault();\n    }\n    const positionStrategy = this.overlay.position().flexibleConnectedTo({\n      x,\n      y\n    }).withPositions(LIST_OF_POSITIONS).withTransformOriginOn('.ant-dropdown');\n    this.overlayRef = this.overlay.create({\n      positionStrategy,\n      disposeOnNavigation: true,\n      scrollStrategy: this.overlay.scrollStrategies.close()\n    });\n    this.closeSubscription = new Subscription();\n    this.closeSubscription.add(nzDropdownMenuComponent.descendantMenuItemClick$.subscribe(() => this.close()));\n    this.closeSubscription.add(this.ngZone.runOutsideAngular(() => merge(fromEvent(document, 'click').pipe(filter(event => !!this.overlayRef && !this.overlayRef.overlayElement.contains(event.target)), /** handle firefox contextmenu event **/\n    filter(event => event.button !== 2)), fromEvent(document, 'keydown').pipe(filter(event => event.key === 'Escape'))).pipe(first()).subscribe(() => this.ngZone.run(() => this.close()))));\n    return this.overlayRef.attach(new TemplatePortal(nzDropdownMenuComponent.templateRef, nzDropdownMenuComponent.viewContainerRef));\n  }\n  close(clear = false) {\n    if (this.overlayRef) {\n      this.overlayRef.detach();\n      if (clear) {\n        this.overlayRef.dispose();\n      }\n      this.overlayRef = null;\n      this.closeSubscription.unsubscribe();\n    }\n  }\n  static {\n    this.ɵfac = function NzContextMenuService_Factory(t) {\n      return new (t || NzContextMenuService)(i0.ɵɵinject(i0.NgZone), i0.ɵɵinject(i2.Overlay));\n    };\n  }\n  static {\n    this.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n      token: NzContextMenuService,\n      factory: NzContextMenuService.ɵfac,\n      providedIn: NzContextMenuServiceModule\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(NzContextMenuService, [{\n    type: Injectable,\n    args: [{\n      providedIn: NzContextMenuServiceModule\n    }]\n  }], () => [{\n    type: i0.NgZone\n  }, {\n    type: i2.Overlay\n  }], null);\n})();\n\n/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { NzContextMenuService, NzContextMenuServiceModule, NzDropDownADirective, NzDropDownDirective, NzDropDownModule, NzDropdownButtonDirective, NzDropdownMenuComponent };\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAsBA,IAAM,MAAM,CAAC,GAAG;AAChB,SAAS,+CAA+C,IAAI,KAAK;AAC/D,MAAI,KAAK,GAAG;AACV,UAAM,MAAS,iBAAiB;AAChC,IAAG,eAAe,GAAG,OAAO,CAAC;AAC7B,IAAG,WAAW,qBAAqB,SAAS,yFAAyF,QAAQ;AAC3I,MAAG,cAAc,GAAG;AACpB,YAAM,SAAY,cAAc;AAChC,aAAU,YAAY,OAAO,iBAAiB,MAAM,CAAC;AAAA,IACvD,CAAC,EAAE,cAAc,SAAS,2EAA2E;AACnG,MAAG,cAAc,GAAG;AACpB,YAAM,SAAY,cAAc;AAChC,aAAU,YAAY,OAAO,cAAc,IAAI,CAAC;AAAA,IAClD,CAAC,EAAE,cAAc,SAAS,2EAA2E;AACnG,MAAG,cAAc,GAAG;AACpB,YAAM,SAAY,cAAc;AAChC,aAAU,YAAY,OAAO,cAAc,KAAK,CAAC;AAAA,IACnD,CAAC;AACD,IAAG,aAAa,CAAC;AACjB,IAAG,aAAa;AAAA,EAClB;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc;AAChC,IAAG,YAAY,oBAAoB,OAAO,QAAQ,KAAK;AACvD,IAAG,WAAW,WAAW,OAAO,kBAAkB,EAAE,WAAW,OAAO,cAAc,EAAE,gBAAgB,MAAS,EAAE,cAAc,CAAC,EAAE,OAAO,eAAe,OAAO,OAAO,OAAO,YAAY,cAAc,EAAE,iBAAiB,OAAO,eAAe,OAAO,OAAO,OAAO,YAAY,aAAa;AAAA,EAChS;AACF;AACA,IAAM,wBAAwB;AAC9B,IAAM,kBAAkB,CAAC,aAAa,YAAY,aAAa,aAAa,aAAa,UAAU,aAAa,OAAO;AACvH,IAAM,sBAAN,MAAM,qBAAoB;AAAA,EACxB,qBAAqB,KAAK,OAAO;AAC/B,QAAI,KAAK,gBAAgB;AACvB,WAAK,eAAe,SAAS,KAAK,KAAK;AAAA,IACzC;AAAA,EACF;AAAA,EACA,YAAY,iBAAiB,YAAY,SAAS,UAAU,kBAAkB,UAAU;AACtF,SAAK,kBAAkB;AACvB,SAAK,aAAa;AAClB,SAAK,UAAU;AACf,SAAK,WAAW;AAChB,SAAK,mBAAmB;AACxB,SAAK,WAAW;AAChB,SAAK,gBAAgB;AACrB,SAAK,aAAa;AAClB,SAAK,WAAW,IAAI,QAAQ;AAC5B,SAAK,mBAAmB,KAAK,QAAQ,SAAS,EAAE,oBAAoB,KAAK,WAAW,aAAa,EAAE,mBAAmB,EAAE,sBAAsB,eAAe;AAC7J,SAAK,gBAAgB,IAAI,gBAAgB,KAAK;AAC9C,SAAK,aAAa,IAAI,gBAAgB,OAAO;AAC7C,SAAK,gBAAgB,IAAI,QAAQ;AACjC,SAAK,iBAAiB;AACtB,SAAK,YAAY;AACjB,SAAK,sBAAsB;AAC3B,SAAK,aAAa;AAClB,SAAK,cAAc;AACnB,SAAK,aAAa;AAClB,SAAK,YAAY;AACjB,SAAK,qBAAqB;AAC1B,SAAK,iBAAiB,CAAC;AACvB,SAAK,cAAc;AACnB,SAAK,kBAAkB,IAAI,aAAa;AAAA,EAC1C;AAAA,EACA,kBAAkB;AAChB,QAAI,KAAK,gBAAgB;AACvB,YAAM,gBAAgB,KAAK,WAAW;AAEtC,YAAM,kBAAkB,MAAM,UAAU,eAAe,YAAY,EAAE,KAAK,MAAM,IAAI,CAAC,GAAG,UAAU,eAAe,YAAY,EAAE,KAAK,MAAM,KAAK,CAAC,CAAC;AAEjJ,YAAM,kBAAkB,KAAK,eAAe;AAE5C,YAAM,oBAAoB,MAAM,iBAAiB,eAAe;AAEhE,YAAM,kBAAkB,UAAU,eAAe,OAAO,EAAE,KAAK,IAAI,MAAM,CAAC,KAAK,SAAS,CAAC;AAEzF,YAAM,yBAAyB,KAAK,WAAW,KAAK,UAAU,aAAW;AACvE,YAAI,YAAY,SAAS;AACvB,iBAAO;AAAA,QACT,WAAW,YAAY,SAAS;AAC9B,iBAAO;AAAA,QACT,OAAO;AACL,iBAAO;AAAA,QACT;AAAA,MACF,CAAC,CAAC;AACF,YAAM,2BAA2B,KAAK,eAAe,yBAAyB,KAAK,OAAO,MAAM,KAAK,WAAW,GAAG,MAAM,KAAK,CAAC;AAC/H,YAAM,qBAAqB,MAAM,wBAAwB,0BAA0B,KAAK,aAAa,EAAE,KAAK,OAAO,MAAM,CAAC,KAAK,UAAU,CAAC;AAC1I,YAAM,WAAW,MAAM,KAAK,eAAe,kBAAkB;AAC7D,oBAAc,CAAC,UAAU,KAAK,eAAe,mBAAmB,CAAC,EAAE,KAAK,IAAI,CAAC,CAAC,SAAS,GAAG,MAAM,WAAW,GAAG,GAAG,UAAU,GAAG,GAAG,qBAAqB,GAAG,OAAO,MAAM,KAAK,SAAS,SAAS,GAAG,UAAU,KAAK,QAAQ,CAAC,EAAE,UAAU,aAAW;AAC7O,cAAM,UAAU,KAAK,sBAAsB,KAAK,oBAAoB,gBAAgB;AACpF,cAAM,eAAe,QAAQ,sBAAsB,EAAE;AACrD,YAAI,KAAK,cAAc,SAAS;AAC9B,eAAK,gBAAgB,KAAK,OAAO;AAAA,QACnC;AACA,aAAK,YAAY;AACjB,YAAI,SAAS;AAEX,cAAI,CAAC,KAAK,YAAY;AAEpB,iBAAK,aAAa,KAAK,QAAQ,OAAO;AAAA,cACpC,kBAAkB,KAAK;AAAA,cACvB,UAAU;AAAA,cACV,qBAAqB;AAAA,cACrB,aAAa,KAAK,cAAc,KAAK,cAAc;AAAA,cACnD,gBAAgB,KAAK,QAAQ,iBAAiB,WAAW;AAAA,YAC3D,CAAC;AACD,kBAAM,KAAK,WAAW,cAAc,GAAG,KAAK,WAAW,YAAY,GAAG,KAAK,WAAW,qBAAqB,EAAE,KAAK,OAAO,OAAK,CAAC,KAAK,WAAW,cAAc,SAAS,EAAE,MAAM,CAAC,CAAC,GAAG,KAAK,WAAW,cAAc,EAAE,KAAK,OAAO,OAAK,EAAE,YAAY,UAAU,CAAC,eAAe,CAAC,CAAC,CAAC,CAAC,EAAE,KAAK,UAAU,KAAK,QAAQ,CAAC,EAAE,UAAU,MAAM;AAC/T,mBAAK,cAAc,KAAK,KAAK;AAAA,YAC/B,CAAC;AAAA,UACH,OAAO;AAEL,kBAAM,gBAAgB,KAAK,WAAW,UAAU;AAChD,0BAAc,WAAW;AAAA,UAC3B;AAEA,eAAK,iBAAiB,cAAc,CAAC,aAAa,KAAK,WAAW,GAAG,GAAG,eAAe,CAAC;AAExF,cAAI,CAAC,KAAK,UAAU,KAAK,OAAO,gBAAgB,KAAK,eAAe,aAAa;AAC/E,iBAAK,SAAS,IAAI,eAAe,KAAK,eAAe,aAAa,KAAK,gBAAgB;AAAA,UACzF;AACA,eAAK,WAAW,OAAO,KAAK,MAAM;AAAA,QACpC,OAAO;AAEL,cAAI,KAAK,YAAY;AACnB,iBAAK,WAAW,OAAO;AAAA,UACzB;AAAA,QACF;AAAA,MACF,CAAC;AACD,WAAK,eAAe,sBAAsB,KAAK,UAAU,KAAK,QAAQ,CAAC,EAAE,UAAU,WAAS;AAC1F,YAAI,MAAM,YAAY,QAAQ;AAC5B,cAAI,KAAK,YAAY;AACnB,iBAAK,WAAW,QAAQ;AAAA,UAC1B;AACA,eAAK,aAAa;AAAA,QACpB;AAAA,MACF,CAAC;AAAA,IACH;AAAA,EACF;AAAA,EACA,cAAc;AACZ,SAAK,SAAS,KAAK,IAAI;AACvB,SAAK,SAAS,SAAS;AACvB,QAAI,KAAK,YAAY;AACnB,WAAK,WAAW,QAAQ;AACxB,WAAK,aAAa;AAAA,IACpB;AAAA,EACF;AAAA,EACA,YAAY,SAAS;AACnB,UAAM;AAAA,MACJ;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACF,IAAI;AACJ,QAAI,WAAW;AACb,WAAK,WAAW,KAAK,KAAK,SAAS;AAAA,IACrC;AACA,QAAI,WAAW;AACb,WAAK,cAAc,KAAK,KAAK,SAAS;AAAA,IACxC;AACA,QAAI,YAAY;AACd,YAAM,gBAAgB,KAAK,WAAW;AACtC,UAAI,KAAK,YAAY;AACnB,aAAK,SAAS,aAAa,eAAe,YAAY,EAAE;AACxD,aAAK,cAAc,KAAK,KAAK;AAAA,MAC/B,OAAO;AACL,aAAK,SAAS,gBAAgB,eAAe,UAAU;AAAA,MACzD;AAAA,IACF;AACA,QAAI,oBAAoB;AACtB,WAAK,qBAAqB,sBAAsB,KAAK,kBAAkB;AAAA,IACzE;AACA,QAAI,gBAAgB;AAClB,WAAK,qBAAqB,kBAAkB,KAAK,cAAc;AAAA,IACjE;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,OAAO,SAAS,4BAA4B,GAAG;AAClD,aAAO,KAAK,KAAK,sBAAwB,kBAAqB,eAAe,GAAM,kBAAqB,UAAU,GAAM,kBAAqB,OAAO,GAAM,kBAAqB,SAAS,GAAM,kBAAqB,gBAAgB,GAAM,kBAAqB,QAAQ,CAAC;AAAA,IACzQ;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,OAAyB,kBAAkB;AAAA,MAC9C,MAAM;AAAA,MACN,WAAW,CAAC,CAAC,IAAI,eAAe,EAAE,CAAC;AAAA,MACnC,WAAW,CAAC,GAAG,sBAAsB;AAAA,MACrC,QAAQ;AAAA,QACN,gBAAgB;AAAA,QAChB,WAAW;AAAA,QACX,qBAAqB;AAAA,QACrB,YAAY;AAAA,QACZ,aAAa;AAAA,QACb,YAAY;AAAA,QACZ,WAAW;AAAA,QACX,oBAAoB;AAAA,QACpB,gBAAgB;AAAA,QAChB,aAAa;AAAA,MACf;AAAA,MACA,SAAS;AAAA,QACP,iBAAiB;AAAA,MACnB;AAAA,MACA,UAAU,CAAC,YAAY;AAAA,MACvB,YAAY;AAAA,MACZ,UAAU,CAAI,oBAAoB;AAAA,IACpC,CAAC;AAAA,EACH;AACF;AACA,WAAW,CAAC,WAAW,GAAG,aAAa,CAAC,GAAG,oBAAoB,WAAW,cAAc,MAAM;AAC9F,WAAW,CAAC,aAAa,CAAC,GAAG,oBAAoB,WAAW,eAAe,MAAM;AACjF,WAAW,CAAC,aAAa,CAAC,GAAG,oBAAoB,WAAW,cAAc,MAAM;AAChF,WAAW,CAAC,aAAa,CAAC,GAAG,oBAAoB,WAAW,aAAa,MAAM;AAAA,CAC9E,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,qBAAqB,CAAC;AAAA,IAC5F,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,UAAU;AAAA,MACV,MAAM;AAAA,QACJ,OAAO;AAAA,MACT;AAAA,MACA,YAAY;AAAA,IACd,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC;AAAA,IACT,MAAS;AAAA,EACX,GAAG;AAAA,IACD,MAAS;AAAA,EACX,GAAG;AAAA,IACD,MAAS;AAAA,EACX,GAAG;AAAA,IACD,MAAS;AAAA,EACX,GAAG;AAAA,IACD,MAAS;AAAA,EACX,GAAG;AAAA,IACD,MAAS;AAAA,EACX,CAAC,GAAG;AAAA,IACF,gBAAgB,CAAC;AAAA,MACf,MAAM;AAAA,IACR,CAAC;AAAA,IACD,WAAW,CAAC;AAAA,MACV,MAAM;AAAA,IACR,CAAC;AAAA,IACD,qBAAqB,CAAC;AAAA,MACpB,MAAM;AAAA,IACR,CAAC;AAAA,IACD,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,IACR,CAAC;AAAA,IACD,aAAa,CAAC;AAAA,MACZ,MAAM;AAAA,IACR,CAAC;AAAA,IACD,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,IACR,CAAC;AAAA,IACD,WAAW,CAAC;AAAA,MACV,MAAM;AAAA,IACR,CAAC;AAAA,IACD,oBAAoB,CAAC;AAAA,MACnB,MAAM;AAAA,IACR,CAAC;AAAA,IACD,gBAAgB,CAAC;AAAA,MACf,MAAM;AAAA,IACR,CAAC;AAAA,IACD,aAAa,CAAC;AAAA,MACZ,MAAM;AAAA,IACR,CAAC;AAAA,IACD,iBAAiB,CAAC;AAAA,MAChB,MAAM;AAAA,IACR,CAAC;AAAA,EACH,CAAC;AACH,GAAG;AAMH,IAAM,6BAAN,MAAM,4BAA2B;AAAA,EAC/B,OAAO;AACL,SAAK,OAAO,SAAS,mCAAmC,GAAG;AACzD,aAAO,KAAK,KAAK,6BAA4B;AAAA,IAC/C;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,OAAyB,iBAAiB;AAAA,MAC7C,MAAM;AAAA,IACR,CAAC;AAAA,EACH;AAAA,EACA,OAAO;AACL,SAAK,OAAyB,iBAAiB,CAAC,CAAC;AAAA,EACnD;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,4BAA4B,CAAC;AAAA,IACnG,MAAM;AAAA,EACR,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;AAMH,IAAM,uBAAN,MAAM,sBAAqB;AAAA,EACzB,cAAc;AAAA,EAAC;AAAA,EACf,OAAO;AACL,SAAK,OAAO,SAAS,6BAA6B,GAAG;AACnD,aAAO,KAAK,KAAK,uBAAsB;AAAA,IACzC;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,OAAyB,kBAAkB;AAAA,MAC9C,MAAM;AAAA,MACN,WAAW,CAAC,CAAC,KAAK,eAAe,EAAE,CAAC;AAAA,MACpC,WAAW,CAAC,GAAG,mBAAmB;AAAA,MAClC,YAAY;AAAA,IACd,CAAC;AAAA,EACH;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,sBAAsB,CAAC;AAAA,IAC7F,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,MAAM;AAAA,QACJ,OAAO;AAAA,MACT;AAAA,MACA,YAAY;AAAA,IACd,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC,GAAG,IAAI;AACpB,GAAG;AAMH,IAAM,4BAAN,MAAM,2BAA0B;AAAA,EAC9B,YAAY,UAAU,wBAAwB,YAAY;AACxD,SAAK,WAAW;AAChB,SAAK,yBAAyB;AAC9B,SAAK,aAAa;AAAA,EACpB;AAAA,EACA,kBAAkB;AAChB,UAAM,gBAAgB,KAAK,SAAS,WAAW,KAAK,WAAW,aAAa;AAC5E,QAAI,KAAK,0BAA0B,eAAe;AAChD,WAAK,SAAS,SAAS,eAAe,qBAAqB;AAAA,IAC7D;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,OAAO,SAAS,kCAAkC,GAAG;AACxD,aAAO,KAAK,KAAK,4BAA8B,kBAAqB,SAAS,GAAM,kBAAuB,wBAAwB,CAAC,GAAM,kBAAqB,UAAU,CAAC;AAAA,IAC3K;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,OAAyB,kBAAkB;AAAA,MAC9C,MAAM;AAAA,MACN,WAAW,CAAC,CAAC,IAAI,aAAa,IAAI,eAAe,EAAE,CAAC;AAAA,MACpD,YAAY;AAAA,IACd,CAAC;AAAA,EACH;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,2BAA2B,CAAC;AAAA,IAClG,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,YAAY;AAAA,IACd,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC;AAAA,IACT,MAAS;AAAA,EACX,GAAG;AAAA,IACD,MAAW;AAAA,IACX,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,IACR,GAAG;AAAA,MACD,MAAM;AAAA,IACR,CAAC;AAAA,EACH,GAAG;AAAA,IACD,MAAS;AAAA,EACX,CAAC,GAAG,IAAI;AACV,GAAG;AACH,IAAM,0BAAN,MAAM,yBAAwB;AAAA,EAC5B,iBAAiB,OAAO;AACtB,SAAK,sBAAsB,KAAK,KAAK;AAAA,EACvC;AAAA,EACA,cAAc,SAAS;AACrB,SAAK,YAAY,KAAK,OAAO;AAAA,EAC/B;AAAA,EACA,SAAS,KAAK,OAAO;AACnB,SAAK,GAAG,IAAI;AACZ,SAAK,IAAI,aAAa;AAAA,EACxB;AAAA,EACA,YAAY,KAAK,YAAY,UAAU,kBAAkB,eAAe,gBAAgB,aAAa;AACnG,SAAK,MAAM;AACX,SAAK,aAAa;AAClB,SAAK,WAAW;AAChB,SAAK,mBAAmB;AACxB,SAAK,gBAAgB;AACrB,SAAK,iBAAiB;AACtB,SAAK,cAAc;AACnB,SAAK,cAAc,IAAI,gBAAgB,KAAK;AAC5C,SAAK,sBAAsB,KAAK,cAAc;AAC9C,SAAK,2BAA2B,KAAK,cAAc;AACnD,SAAK,wBAAwB,IAAI,aAAa;AAC9C,SAAK,qBAAqB;AAC1B,SAAK,iBAAiB,CAAC;AACvB,SAAK,MAAM;AACX,SAAK,WAAW,IAAI,QAAQ;AAAA,EAC9B;AAAA,EACA,WAAW;AACT,SAAK,eAAe,QAAQ,KAAK,UAAU,KAAK,QAAQ,CAAC,EAAE,UAAU,eAAa;AAChF,WAAK,MAAM;AACX,WAAK,IAAI,cAAc;AAAA,IACzB,CAAC;AACD,SAAK,MAAM,KAAK,eAAe;AAAA,EACjC;AAAA,EACA,qBAAqB;AACnB,SAAK,SAAS,YAAY,KAAK,SAAS,WAAW,KAAK,WAAW,aAAa,GAAG,KAAK,WAAW,aAAa;AAAA,EAClH;AAAA,EACA,cAAc;AACZ,SAAK,SAAS,KAAK;AACnB,SAAK,SAAS,SAAS;AAAA,EACzB;AAAA,EACA,OAAO;AACL,SAAK,OAAO,SAAS,gCAAgC,GAAG;AACtD,aAAO,KAAK,KAAK,0BAA4B,kBAAqB,iBAAiB,GAAM,kBAAqB,UAAU,GAAM,kBAAqB,SAAS,GAAM,kBAAqB,gBAAgB,GAAM,kBAAuB,WAAW,GAAM,kBAAuB,gBAAgB,CAAC,GAAM,kBAAuB,wBAAwB,CAAC,CAAC;AAAA,IACtV;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,OAAyB,kBAAkB;AAAA,MAC9C,MAAM;AAAA,MACN,WAAW,CAAC,CAAC,kBAAkB,CAAC;AAAA,MAChC,WAAW,SAAS,8BAA8B,IAAI,KAAK;AACzD,YAAI,KAAK,GAAG;AACV,UAAG,YAAY,aAAa,CAAC;AAAA,QAC/B;AACA,YAAI,KAAK,GAAG;AACV,cAAI;AACJ,UAAG,eAAe,KAAQ,YAAY,CAAC,MAAM,IAAI,cAAc,GAAG;AAAA,QACpE;AAAA,MACF;AAAA,MACA,UAAU,CAAC,gBAAgB;AAAA,MAC3B,YAAY;AAAA,MACZ,UAAU,CAAI,mBAAmB;AAAA,QAAC;AAAA;AAAA,QAClC;AAAA,UACE,SAAS;AAAA,UACT,UAAU;AAAA,QACZ;AAAA,MAAC,CAAC,GAAM,mBAAmB;AAAA,MAC3B,oBAAoB;AAAA,MACpB,OAAO;AAAA,MACP,MAAM;AAAA,MACN,QAAQ,CAAC,CAAC,GAAG,gBAAgB,GAAG,cAAc,cAAc,WAAW,WAAW,eAAe,CAAC;AAAA,MAClG,UAAU,SAAS,iCAAiC,IAAI,KAAK;AAC3D,YAAI,KAAK,GAAG;AACV,UAAG,gBAAgB;AACnB,UAAG,WAAW,GAAG,gDAAgD,GAAG,GAAG,aAAa;AAAA,QACtF;AAAA,MACF;AAAA,MACA,cAAc,CAAC,SAAS,SAAS,sBAAsB;AAAA,MACvD,eAAe;AAAA,MACf,MAAM;AAAA,QACJ,WAAW,CAAC,WAAW;AAAA,MACzB;AAAA,MACA,iBAAiB;AAAA,IACnB,CAAC;AAAA,EACH;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,yBAAyB,CAAC;AAAA,IAChG,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,UAAU;AAAA,MACV,YAAY,CAAC,WAAW;AAAA,MACxB,WAAW;AAAA,QAAC;AAAA;AAAA,QACZ;AAAA,UACE,SAAS;AAAA,UACT,UAAU;AAAA,QACZ;AAAA,MAAC;AAAA,MACD,UAAU;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MAkBV,qBAAqB;AAAA,MACrB,eAAe,oBAAkB;AAAA,MACjC,iBAAiB,wBAAwB;AAAA,MACzC,SAAS,CAAC,SAAS,SAAS,sBAAsB;AAAA,MAClD,YAAY;AAAA,IACd,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC;AAAA,IACT,MAAS;AAAA,EACX,GAAG;AAAA,IACD,MAAS;AAAA,EACX,GAAG;AAAA,IACD,MAAS;AAAA,EACX,GAAG;AAAA,IACD,MAAS;AAAA,EACX,GAAG;AAAA,IACD,MAAW;AAAA,EACb,GAAG;AAAA,IACD,MAAW;AAAA,IACX,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,IACR,CAAC;AAAA,EACH,GAAG;AAAA,IACD,MAAW;AAAA,IACX,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,IACR,GAAG;AAAA,MACD,MAAM;AAAA,IACR,CAAC;AAAA,EACH,CAAC,GAAG;AAAA,IACF,aAAa,CAAC;AAAA,MACZ,MAAM;AAAA,MACN,MAAM,CAAC,aAAa;AAAA,QAClB,QAAQ;AAAA,MACV,CAAC;AAAA,IACH,CAAC;AAAA,EACH,CAAC;AACH,GAAG;AAMH,IAAM,mBAAN,MAAM,kBAAiB;AAAA,EACrB,OAAO;AACL,SAAK,OAAO,SAAS,yBAAyB,GAAG;AAC/C,aAAO,KAAK,KAAK,mBAAkB;AAAA,IACrC;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,OAAyB,iBAAiB;AAAA,MAC7C,MAAM;AAAA,MACN,SAAS,CAAC,qBAAqB,sBAAsB,yBAAyB,2BAA2B,0BAA0B;AAAA,MACnI,SAAS,CAAC,cAAc,qBAAqB,sBAAsB,yBAAyB,yBAAyB;AAAA,IACvH,CAAC;AAAA,EACH;AAAA,EACA,OAAO;AACL,SAAK,OAAyB,iBAAiB;AAAA,MAC7C,SAAS,CAAC,4BAA4B,YAAY;AAAA,IACpD,CAAC;AAAA,EACH;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,kBAAkB,CAAC;AAAA,IACzF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,SAAS,CAAC,qBAAqB,sBAAsB,yBAAyB,2BAA2B,0BAA0B;AAAA,MACnI,SAAS,CAAC,cAAc,qBAAqB,sBAAsB,yBAAyB,yBAAyB;AAAA,IACvH,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;AAMH,IAAM,oBAAoB,CAAC,IAAI,uBAAuB;AAAA,EACpD,SAAS;AAAA,EACT,SAAS;AACX,GAAG;AAAA,EACD,UAAU;AAAA,EACV,UAAU;AACZ,CAAC,GAAG,IAAI,uBAAuB;AAAA,EAC7B,SAAS;AAAA,EACT,SAAS;AACX,GAAG;AAAA,EACD,UAAU;AAAA,EACV,UAAU;AACZ,CAAC,GAAG,IAAI,uBAAuB;AAAA,EAC7B,SAAS;AAAA,EACT,SAAS;AACX,GAAG;AAAA,EACD,UAAU;AAAA,EACV,UAAU;AACZ,CAAC,GAAG,IAAI,uBAAuB;AAAA,EAC7B,SAAS;AAAA,EACT,SAAS;AACX,GAAG;AAAA,EACD,UAAU;AAAA,EACV,UAAU;AACZ,CAAC,CAAC;AACF,IAAM,uBAAN,MAAM,sBAAqB;AAAA,EACzB,YAAY,QAAQ,SAAS;AAC3B,SAAK,SAAS;AACd,SAAK,UAAU;AACf,SAAK,aAAa;AAClB,SAAK,oBAAoB,aAAa;AAAA,EACxC;AAAA,EACA,OAAO,QAAQ,yBAAyB;AACtC,SAAK,MAAM,IAAI;AACf,UAAM;AAAA,MACJ;AAAA,MACA;AAAA,IACF,IAAI;AACJ,QAAI,kBAAkB,YAAY;AAChC,aAAO,eAAe;AAAA,IACxB;AACA,UAAM,mBAAmB,KAAK,QAAQ,SAAS,EAAE,oBAAoB;AAAA,MACnE;AAAA,MACA;AAAA,IACF,CAAC,EAAE,cAAc,iBAAiB,EAAE,sBAAsB,eAAe;AACzE,SAAK,aAAa,KAAK,QAAQ,OAAO;AAAA,MACpC;AAAA,MACA,qBAAqB;AAAA,MACrB,gBAAgB,KAAK,QAAQ,iBAAiB,MAAM;AAAA,IACtD,CAAC;AACD,SAAK,oBAAoB,IAAI,aAAa;AAC1C,SAAK,kBAAkB,IAAI,wBAAwB,yBAAyB,UAAU,MAAM,KAAK,MAAM,CAAC,CAAC;AACzG,SAAK,kBAAkB,IAAI,KAAK,OAAO,kBAAkB,MAAM,MAAM,UAAU,UAAU,OAAO,EAAE;AAAA,MAAK,OAAO,WAAS,CAAC,CAAC,KAAK,cAAc,CAAC,KAAK,WAAW,eAAe,SAAS,MAAM,MAAM,CAAC;AAAA;AAAA,MAClM,OAAO,WAAS,MAAM,WAAW,CAAC;AAAA,IAAC,GAAG,UAAU,UAAU,SAAS,EAAE,KAAK,OAAO,WAAS,MAAM,QAAQ,QAAQ,CAAC,CAAC,EAAE,KAAK,MAAM,CAAC,EAAE,UAAU,MAAM,KAAK,OAAO,IAAI,MAAM,KAAK,MAAM,CAAC,CAAC,CAAC,CAAC;AACvL,WAAO,KAAK,WAAW,OAAO,IAAI,eAAe,wBAAwB,aAAa,wBAAwB,gBAAgB,CAAC;AAAA,EACjI;AAAA,EACA,MAAM,QAAQ,OAAO;AACnB,QAAI,KAAK,YAAY;AACnB,WAAK,WAAW,OAAO;AACvB,UAAI,OAAO;AACT,aAAK,WAAW,QAAQ;AAAA,MAC1B;AACA,WAAK,aAAa;AAClB,WAAK,kBAAkB,YAAY;AAAA,IACrC;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,OAAO,SAAS,6BAA6B,GAAG;AACnD,aAAO,KAAK,KAAK,uBAAyB,SAAY,MAAM,GAAM,SAAY,OAAO,CAAC;AAAA,IACxF;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,QAA0B,mBAAmB;AAAA,MAChD,OAAO;AAAA,MACP,SAAS,sBAAqB;AAAA,MAC9B,YAAY;AAAA,IACd,CAAC;AAAA,EACH;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,sBAAsB,CAAC;AAAA,IAC7F,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,YAAY;AAAA,IACd,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC;AAAA,IACT,MAAS;AAAA,EACX,GAAG;AAAA,IACD,MAAS;AAAA,EACX,CAAC,GAAG,IAAI;AACV,GAAG;", "names": []}